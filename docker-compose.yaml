services:

  # 🐘 Symfony App
  asnmx:
    build:
      context: .
      dockerfile: .docker/Dockerfile
    restart: unless-stopped
    ports:
      - "8004:80"
    networks:
      - database
    volumes:
      - .:/var/www/html
    env_file:
      - .docker/env/docker.env
    depends_on:
      mysql:
        condition: service_healthy
      mailer:
        condition: service_started

  # 🐬 MySQL DB
  mysql:
    image: mysql:8.0.35
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    ports:
      - "33306:3306"
    networks:
      - database
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: user
      MYSQL_PASSWORD: root
    volumes:
      - mysql:/var/lib/mysql
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-proot"]
      interval: 10s
      timeout: 5s
      retries: 10
  # 🐘 phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - database
    environment:
      PMA_HOST: mysql
      UPLOAD_LIMIT: 100M
      PMA_ABSOLUTE_URI: ""
    depends_on:
      - mysql

  # 📬 Mailpit (SMTP test server)
  mailer:
    image: axllent/mailpit
    restart: unless-stopped
    container_name: mailpit
    ports:
      - "1025:1025"   # SMTP
      - "8025:8025"   # Web UI
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    networks:
      - database

  # ⚡ Mercure (real-time pub/sub)
  mercure:
    image: dunglas/mercure
    restart: unless-stopped
    environment:
      SERVER_NAME: ':80'
      MERCURE_PUBLISHER_JWT_KEY: SANFEAFcV9JQTKlutJx3cAtudhAGHHkBCSdt3vDxINM=
      MERCURE_SUBSCRIBER_JWT_KEY: SANFEAFcV9JQTKlutJx3cAtudhAGHHkBCSdt3vDxINM=
      MERCURE_CORS_ALLOWED_ORIGINS: 'http://localhost:8004 http://***************:8004'
      MERCURE_ALLOW_ANONYMOUS: '1'
      MERCURE_EXTRA_DIRECTIVES: |
        cors_origins http://localhost:8004 http://***************:8004
    ports:
      - "1337:80"
    volumes:
      - mercure_data:/data
      - mercure_config:/config
    networks:
      - database

volumes:
  mysql:
  mercure_data:
  mercure_config:

networks:
  database:
    driver: bridge
