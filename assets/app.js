import './styles/app.css';

import './chart/columnChart.js';
import './chart/barChartAge.js';
import './chart/barChartSchooling.js';

import './calendar/fullcalendar';

import $ from 'jquery';
window.$ = $;
window.jQuery = $;

import 'select2/dist/css/select2.min.css';

import { initSelect2Multiple } from './select/initSelect2.js';

import 'bootstrap';

import 'bootstrap/dist/css/bootstrap.min.css';

import 'bootstrap/dist/js/bootstrap.bundle.min.js';


document.addEventListener('DOMContentLoaded', () => {
    initSelect2Multiple();
});