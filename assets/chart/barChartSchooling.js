import ApexCharts from 'apexcharts';

document.addEventListener("DOMContentLoaded", () => {
  const chartEl = document.querySelector("#chart-3");

  if (!chartEl) {
    console.warn("❌ No se encontró el elemento #chart-3, se omite renderizado de ApexChart.");
    return;
  }

  const options = {
    series: [
      { name: 'HOMBRES', data: [0.4, 0.65, 0.76, 0.88, 1.5, 2.1, 2.9] },
      { name: 'MUJERES', data: [-0.8, -1.05, -1.06, -1.18, -1.4, -2.2, -2.85] }
    ],
    chart: {
      type: 'bar',
      height: 200,
      width: '100%',
      stacked: true.valueOf,
      className: 'people-information',
      toolbar: {
        show: false
      },
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 800
      }
    },
    colors: ['#FFFFFF'],
    plotOptions: {
      bar: {
        borderRadius: 5,
        borderRadiusApplication: 'start',
        borderRadiusWhenStacked: 'all',
        horizontal: true,
        barHeight: '70%'
      }
    },
    dataLabels: { enabled: false },
    stroke: { width: 1, colors: ["#FFFFFF"] },
    xaxis: {
      categories: ['MAESTRIA', 'POSGRADO', 'UNIVERSISDAD', 'PREPARATORIA', 'SECUNDARIA', 'PRIMARIA', 'PRESCOLAR',],
      labels: {
        show: false
      },
      axisTicks: {
        show: false 
      },
      axisBorder: {
        show: false 
      }
    },
    yaxis: {
      title: { 
        text: 'ESCOLARIDAD',
        style: {
          color: '#FFFFFF',
        },
      },
      labels: {
        style: {
          colors: Array(7).fill('#FFFFFF'),
        }
      }
    },
    tooltip: {
      y: {
        formatter: val => Math.abs(val) + "%"
      }
    },
    grid: {
      yaxis: {
        lines: {
          show: false
        }
      }
    },
    legend: {
      show: true,
      position: 'bottom',
      horizontalAlign: 'center',
      labels: {
        colors: ['#FFFFFF', '#FFFFFF'],
        useSeriesColors: false
      }
    },
    breakpoint: 768,
    options: {
      chart: {
        height: 150
      },
      plotOptions: {
        bar: {
          barHeight: '30%'
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      }
    }  
  };

  new ApexCharts(document.querySelector("#chart-3"), options).render();
});
