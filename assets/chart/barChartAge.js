import ApexCharts from 'apexcharts';

document.addEventListener("DOMContentLoaded", () => {
  const chartEl = document.querySelector("#chart-2");

  if (!chartEl) {
    console.warn("❌ No se encontró el elemento #chart-2, se omite renderizado de ApexChart.");
    return;
  }

  // Get age distribution data from the global variable or use default values if not available
  const ageData = window.ageDistributionData || {
    categories: ['78-85', '68-75', '58-68', '48-58', '38-48', '28-38', '18-28'],
    men: [0.4, 0.65, 0.76, 0.88, 1.5, 2.1, 2.9],
    women: [0.8, 1.05, 1.06, 1.18, 1.4, 2.2, 2.85]
  };

  // Make women's values negative for the mirror effect
  const womenNegativeValues = ageData.women.map(value => -value);

  const options = {
    series: [
      { name: 'HOMBR<PERSON>', data: ageData.men },
      { name: 'MUJER<PERSON>', data: womenNegativeValues }
    ],
    chart: {
      type: 'bar',
      height: 200,
      width: '100%',
      stacked: true,
      className: 'people-information',
      toolbar: {
        show: false
      },
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 800
      }
    },
    colors: ['#FFFFFF'],
    plotOptions: {
      bar: {
        borderRadius: 5,
        borderRadiusApplication: 'start',
        borderRadiusWhenStacked: 'all',
        horizontal: true,
        barHeight: '70%'
      }
    },
    dataLabels: { enabled: false },
    stroke: { width: 1, colors: ["#FFFFFF"] },
    xaxis: {
      categories: ageData.categories,
      labels: {
        show: false
      },
      axisTicks: {
        show: false 
      },
      axisBorder: {
        show: false 
      }
    },
    yaxis: {
      title: { 
        text: 'EDAD',
        style: {
          color: '#FFFFFF',
        },
        className: 'title-chart',
      },
      labels: {
        style: {
          colors: Array(7).fill('#FFFFFF'),
        }
      }
    },
    tooltip: {
      y: {
        formatter: val => Math.abs(val)
      }
    },
    grid: {
      yaxis: {
        lines: {
          show: false
        }
      }
    },
    legend: {
      show: true,
      position: 'bottom',
      horizontalAlign: 'center',
      labels: {
        colors: ['#FFFFFF', '#FFFFFF'],
        useSeriesColors: false
      }
    },
    breakpoint: 768,
    options: {
      chart: {
        height: 150
      },
      plotOptions: {
        bar: {
          barHeight: '30%'
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      }
    }
  };

  new ApexCharts(document.querySelector("#chart-2"), options).render();
});
