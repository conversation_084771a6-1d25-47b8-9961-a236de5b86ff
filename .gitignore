
###> symfony/framework-bundle ###
/.env.local
/.env.dev
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> lexik/jwt-authentication-bundle ###
/config/jwt/*.pem
.idea/workspace.xml
###< lexik/jwt-authentication-bundle ###

/public/uploads/
/config/firebase/*.json
###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

###> symfony/asset-mapper ###
/public/assets/
/assets/vendor/
###< symfony/asset-mapper ###

###> lexik/jwt-authentication-bundle ###
/config/jwt/*.pem
.docker/jwt/
###< lexik/jwt-authentication-bundle ###

/public/uploads/
/config/firebase/*.json
###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

###> phpunit/phpunit ###
/phpunit.xml
/.phpunit.cache/
/package-lock.json
###< phpunit/phpunit ###
###< phpunit/phpunit ###

###> documentación del proyecto ###
# Documentación técnica y de desarrollo
/documentos/
/docs/
*.md
!README.md
!CHANGELOG.md
!CONTRIBUTING.md
!LICENSE.md

# Scripts de verificación y utilidades de desarrollo
/bin/verify-*.php
/bin/check-*.php
/bin/dev-*.php

# Archivos de análisis y reportes
/reports/
/analysis/
*.log.md
*.report.md
###< documentación del proyecto ###

.env.dev
docker.env
.idea