#!/bin/bash

echo "🚀 Iniciando entrypoint de la aplicación..."
#!/bin/bash
set -e

cd /var/www/html

# ⚠️ PROTECCIÓN CONTRA PÉRDIDA DE DATOS EN PRODUCCIÓN ⚠️
if [ "$APP_ENV" = "prod" ]; then
    echo "🔒 MODO PRODUCCIÓN DETECTADO - Protecciones de datos activadas"
    # Verificar que no se ejecuten comandos destructivos accidentalmente
    export DOCTRINE_DISABLE_SCHEMA_DROP=1
fi

# Esperar a que MySQL esté disponible (por hostname y por DSN)
echo "Esperando a que MySQL (mysql:3306) esté disponible..."
until mysqladmin ping -h"mysql" -P3306 --silent; do
    sleep 2
    echo "Esperando a MySQL (mysql:3306)..."
done

# Verificación adicional con PHP (opcional)
echo "Verificación de conexión MySQL completada."

# Ejecutar migraciones automáticamente
echo "=== CONFIGURANDO BASE DE DATOS ==="

# Crear bases de datos si no existen
echo "🗄️ Creando bases de datos..."
php bin/console doctrine:database:create --if-not-exists --env=prod || true

# Ejecutar migraciones para tenant TS
echo "🗄️ Configurando esquema para tenant TS..."

# Verificar si la base de datos ya tiene tablas (para evitar borrar datos existentes)
TABLES_COUNT=$(php bin/console dbal:run-sql "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()" --env=prod 2>/dev/null | grep -o '[0-9]\+' | tail -1 || echo "0")

if [ "$TABLES_COUNT" -gt 0 ]; then
    echo "✅ Base de datos existente detectada con $TABLES_COUNT tablas. Ejecutando migraciones..."
    # Solo ejecutar migraciones, NUNCA borrar datos existentes
    php bin/console doctrine:migrations:migrate --no-interaction --env=prod || {
        echo "⚠️ Error en migraciones. Verificando estado de la base de datos..."
        php bin/console doctrine:migrations:status --env=prod || true
        echo "🔧 Intentando sincronizar estado de migraciones..."
        php bin/console doctrine:migrations:version --add --all --no-interaction --env=prod || true
    }
else
    echo "🆕 Base de datos vacía detectada. Creando schema inicial..."
    # Solo para bases de datos completamente nuevas
    php bin/console doctrine:schema:create --env=prod || {
        echo "⚠️ Error creando schema. Intentando con migraciones..."
        php bin/console doctrine:migrations:migrate --no-interaction --env=prod || true
    }
    php bin/console doctrine:migrations:version --add --all --no-interaction --env=prod || true
fi

# Ejecutar migraciones para tenant SNT si la variable existe
if [ -n "$DATABASE_URL_SNT" ]; then
    echo "🗄️ Configurando esquema para tenant SNT..."
    DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:database:create --if-not-exists --env=prod || true

    # Verificar si la base de datos SNT ya tiene tablas
    SNT_TABLES_COUNT=$(DATABASE_URL="$DATABASE_URL_SNT" php bin/console dbal:run-sql "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()" --env=prod 2>/dev/null | grep -o '[0-9]\+' | tail -1 || echo "0")

    if [ "$SNT_TABLES_COUNT" -gt 0 ]; then
        echo "✅ Base de datos SNT existente detectada con $SNT_TABLES_COUNT tablas. Ejecutando migraciones..."
        # Solo ejecutar migraciones, NUNCA borrar datos existentes
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:migrate --no-interaction --env=prod || {
            echo "⚠️ Error en migraciones SNT. Verificando estado..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:status --env=prod || true
            echo "🔧 Intentando sincronizar estado de migraciones SNT..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:version --add --all --no-interaction --env=prod || true
        }
    else
        echo "🆕 Base de datos SNT vacía detectada. Creando schema inicial..."
        # Solo para bases de datos completamente nuevas
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:schema:create --env=prod || {
            echo "⚠️ Error creando schema SNT. Intentando con migraciones..."
            DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:migrate --no-interaction --env=prod || true
        }
        DATABASE_URL="$DATABASE_URL_SNT" php bin/console doctrine:migrations:version --add --all --no-interaction --env=prod || true
    fi
fi

# Limpiar cache
echo "🧹 Limpiando cache..."
php bin/console cache:clear --env=prod

echo "✅ Configuración de base de datos completada."

# Ejecutar comandos pasados al entrypoint (por ejemplo, bash interactivo)
if [ "$1" != "apache2-foreground" ]; then
  exec "$@"
fi

# Composer install solo si falta vendor/autoload.php
if [ ! -f vendor/autoload.php ]; then
  echo "Ejecutando composer install..."
  composer install --no-interaction --optimize-autoloader || { echo "composer install falló"; exit 1; }
fi

# Limpiar caché Symfony
echo "Limpiando caché Symfony..."
php bin/console cache:clear --env=prod --no-debug || { echo "cache:clear falló"; exit 1; }

# Crear directorio para las claves JWT si no existe
mkdir -p config/jwt

# Generar las claves JWT si no existen
if [ ! -f config/jwt/private.pem ]; then
    php bin/console lexik:jwt:generate-keypair --skip-if-exists --no-interaction
fi

# Asegurarse que los directorios tienen los permisos correctos
chown -R www-data:www-data var/ config/jwt/
chmod -R 777 var/
chmod 644 config/jwt/private.pem config/jwt/public.pem

# Iniciar Apache
apache2-foreground