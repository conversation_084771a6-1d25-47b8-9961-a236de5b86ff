<IfModule mod_rewrite.c>
    RewriteEngine on
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    RewriteCond %{REQUEST_FILENAME} -s [OR]
    RewriteCond %{REQUEST_FILENAME} -l [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^.*$ - [NC,L]
    RewriteRule ^(.*) index.php [NC,L]
</IfModule>

# PHP Configuration
php_value max_execution_time 300
php_value session.gc_maxlifetime 3600
php_value memory_limit 512M
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_file_uploads 20
php_value max_input_vars 1000