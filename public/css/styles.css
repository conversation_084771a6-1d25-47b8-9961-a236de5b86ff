.index {
  background-image: url("../images/principalFondo.jpg");
  background-size: cover;
  background-position: center;
}

.buttons {
  text-decoration: none;
  background: #90D63B;
  padding: 25px 45px 25px 45px; 
  border-radius: 15px; 
  font-weight: bold;
  box-shadow: 10px 10px 10px rgba(42, 48, 44, 0.623);
  display: inline-block; 
  text-align: center; 
  color: white; 
}
.buttons:hover {
  background: #daddd6;
  box-shadow: 6px 5px 3px rgba(66, 82, 68, 0.5);
  transition: 0.3s ease-in-out;
}
.button {
  text-decoration: none;
  background: #90D63B;
  padding: 15px 25px 15px 25px; 
  border-radius: 15px; 
  font-weight: bold;
  box-shadow: 10px 10px 10px rgba(42, 48, 44, 0.623);
  display: inline-block; 
  text-align: center; 
  color: white; 
}
.button:hover {
  background: #daddd6;
  box-shadow: 6px 5px 3px rgba(66, 82, 68, 0.5);
  transition: 0.3s ease-in-out;
}
.form {
  background-image: url("../images/secundarioFondo.png");
  background-size: cover;
  background-position: center;

}
.form {
  background-image: url("../images/secundarioFondo.png");
  background-size: cover;
  background-position: center;

}
video, canvas, img {
      max-width: 100%;
      border-radius: 10px;
      margin: 10px 0;
    }
    #preview {
      width: 200px;
      height: auto;
      display: block;
      margin: 10px auto;
    }
input {
  width: 100%;
  padding: 10px;
  border-radius: 25px; 
  font-size: 16px;

}

.form-control  {

  width: 100%;
  padding: 15px;
  border-radius: 25px; 
}
input[type="text"],
input[type="file"]{
  
  width: 100%;
  padding: 10px;
  border-radius: 25px; 
  font-size: 16px;
  outline: none;
  text-transform: uppercase;

}

input[type="file"]
{
  background-color: #7CC13E; 
  color: #000000;
  border-radius: 30px; 
  padding: 5px; 
  border: none;
}
.modal-content, .card-body,.shadow-lg {
  background-color: #b1afaf8c; 
  border: none; 
  box-shadow: none; 
}

.modal-backdrop {
  background-color: rgba(255, 255, 255, 0); 
}

.modal-body {
  background-color: #f1ededfd; 
  padding: 10px;
  border-radius: 25px; 
}

.end{
  background-color: rgba(255, 255, 255, 0); 
}