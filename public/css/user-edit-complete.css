/* Estilos adicionales para la vista completa de edición de usuarios */

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Contenedor principal */
.user-edit-container {
    animation: fadeIn 0.6s ease-out;
}

/* Sidebar mejorado */
.sidebar {
    animation: slideIn 0.5s ease-out;
    position: relative;
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.user-avatar:hover,
.user-avatar-placeholder:hover {
    animation: pulse 0.6s ease-in-out;
    cursor: pointer;
}

/* Estadísticas del usuario */
.user-stats {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.stat-item {
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255,255,255,0.1);
    border-radius: 5px;
    padding: 5px;
    margin: -5px;
}

/* Enlaces rápidos mejorados */
.quick-actions a {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.quick-actions a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.quick-actions a:hover::before {
    left: 100%;
}

.quick-actions a:hover {
    border-left-color: #fff;
    padding-left: 15px;
    background: rgba(255,255,255,0.15);
}

/* Área de contenido */
.content-area {
    animation: fadeIn 0.8s ease-out 0.2s both;
}

/* Breadcrumbs mejorados */
.breadcrumb-custom {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 10px;
    padding: 15px 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Secciones colapsables */
.section-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.section-header {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.section-header:hover::before {
    left: 100%;
}

.section-header:hover {
    background: linear-gradient(45deg, #5a67d8, #6b46c1);
}

.collapse-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(-90deg);
}

/* Formularios mejorados */
.form-control-custom {
    transition: all 0.3s ease;
    background: #fff;
    position: relative;
}

.form-control-custom:focus {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.form-group-custom label {
    transition: color 0.3s ease;
}

.form-group-custom:hover label {
    color: #667eea;
}

/* Regiones container */
.regions-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6 !important;
    transition: all 0.3s ease;
}

.regions-container:hover {
    border-color: #667eea !important;
    background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
}

/* Botones mejorados */
.btn-primary-custom {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn-primary-custom:hover::before {
    left: 100%;
}

/* Tablas mejoradas */
.table-hover tbody tr {
    transition: all 0.3s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1) !important;
    transform: scale(1.02);
}

.form-responses-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-responses-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-responses-table td {
    border-color: rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.form-responses-table tbody tr:hover td {
    background: rgba(102, 126, 234, 0.05);
}

/* Badges de estado */
.status-badge {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.status-completed {
    border-color: #28a745;
}

.status-pending {
    border-color: #ffc107;
}

.status-badge:hover {
    transform: scale(1.1);
}

/* Modal mejorado */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* Efectos de carga */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Responsive mejorado */
@media (max-width: 992px) {
    .sidebar {
        animation: fadeIn 0.5s ease-out;
    }
    
    .section-card {
        margin-bottom: 15px;
    }
    
    .user-avatar,
    .user-avatar-placeholder {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }
}

@media (max-width: 768px) {
    .content-area {
        padding: 15px;
    }
    
    .section-header h5 {
        font-size: 16px;
    }
    
    .btn-primary-custom,
    .btn-secondary-custom {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .form-responses-table {
        font-size: 12px;
    }
    
    .user-stats {
        padding: 15px;
    }
    
    .stat-item {
        font-size: 12px;
    }
}

/* Scroll suave */
html {
    scroll-behavior: smooth;
}

/* Efectos de enfoque para accesibilidad */
.section-header:focus,
.quick-actions a:focus,
.btn-primary-custom:focus,
.btn-secondary-custom:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Indicador de carga para formularios */
.form-loading {
    position: relative;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Mejoras para impresión */
@media print {
    .sidebar,
    .breadcrumb-custom,
    .btn-primary-custom,
    .btn-secondary-custom {
        display: none !important;
    }
    
    .content-area {
        padding: 0;
    }
    
    .section-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}
