<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250709170216 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE UnreadMessage (id INT AUTO_INCREMENT NOT NULL, conversation_id INT NOT NULL, message_id INT NOT NULL, INDEX IDX_B0E7B4CB9AC0396 (conversation_id), UNIQUE INDEX UNIQ_B0E7B4CB537A1329 (message_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE UnreadMessage ADD CONSTRAINT FK_B0E7B4CB9AC0396 FOREIGN KEY (conversation_id) REFERENCES Conversation (id)');
        $this->addSql('ALTER TABLE UnreadMessage ADD CONSTRAINT FK_B0E7B4CB537A1329 FOREIGN KEY (message_id) REFERENCES Message (id)');
        $this->addSql('ALTER TABLE Message DROP unread');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE UnreadMessage DROP FOREIGN KEY FK_B0E7B4CB9AC0396');
        $this->addSql('ALTER TABLE UnreadMessage DROP FOREIGN KEY FK_B0E7B4CB537A1329');
        $this->addSql('DROP TABLE UnreadMessage');
        $this->addSql('ALTER TABLE Message ADD unread TINYINT(1) DEFAULT NULL');
    }
}
