<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250709175932 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE lock_keys (key_id VARCHAR(64) NOT NULL, key_token VARCHAR(44) NOT NULL, key_expiration INT UNSIGNED NOT NULL, PRIMARY KEY(key_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE Chat ADD CONSTRAINT FK_C5AF5D94979B1AD6 FOREIGN KEY (company_id) REFERENCES Company (id)');
        $this->addSql('ALTER TABLE Chat ADD CONSTRAINT FK_C5AF5D94F624B39D FOREIGN KEY (sender_id) REFERENCES User (id)');
        $this->addSql('ALTER TABLE Chat ADD CONSTRAINT FK_C5AF5D94386D8D01 FOREIGN KEY (receptor_id) REFERENCES User (id)');
        $this->addSql('ALTER TABLE form_template_company DROP FOREIGN KEY FK_FTC_FORM_TEMPLATE_ID');
        $this->addSql('DROP INDEX IDX_FTC_FORM_TEMPLATE ON form_template_company');
        $this->addSql('DROP INDEX `primary` ON form_template_company');
        $this->addSql('ALTER TABLE form_template_company CHANGE form_template_id formtemplate_id INT NOT NULL');
        $this->addSql('ALTER TABLE form_template_company ADD CONSTRAINT FK_E3B87E5B6F56B354 FOREIGN KEY (formtemplate_id) REFERENCES FormTemplate (id) ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_E3B87E5B6F56B354 ON form_template_company (formtemplate_id)');
        $this->addSql('ALTER TABLE form_template_company ADD PRIMARY KEY (formtemplate_id, company_id)');
        $this->addSql('ALTER TABLE form_template_company RENAME INDEX idx_ftc_company TO IDX_E3B87E5B979B1AD6');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE lock_keys');
        $this->addSql('ALTER TABLE Chat DROP FOREIGN KEY FK_C5AF5D94979B1AD6');
        $this->addSql('ALTER TABLE Chat DROP FOREIGN KEY FK_C5AF5D94F624B39D');
        $this->addSql('ALTER TABLE Chat DROP FOREIGN KEY FK_C5AF5D94386D8D01');
        $this->addSql('ALTER TABLE form_template_company DROP FOREIGN KEY FK_E3B87E5B6F56B354');
        $this->addSql('DROP INDEX IDX_E3B87E5B6F56B354 ON form_template_company');
        $this->addSql('DROP INDEX `PRIMARY` ON form_template_company');
        $this->addSql('ALTER TABLE form_template_company CHANGE formtemplate_id form_template_id INT NOT NULL');
        $this->addSql('ALTER TABLE form_template_company ADD CONSTRAINT FK_FTC_FORM_TEMPLATE_ID FOREIGN KEY (form_template_id) REFERENCES FormTemplate (id) ON UPDATE NO ACTION ON DELETE CASCADE');
        $this->addSql('CREATE INDEX IDX_FTC_FORM_TEMPLATE ON form_template_company (form_template_id)');
        $this->addSql('ALTER TABLE form_template_company ADD PRIMARY KEY (form_template_id, company_id)');
        $this->addSql('ALTER TABLE form_template_company RENAME INDEX idx_e3b87e5b979b1ad6 TO IDX_FTC_COMPANY');
    }
}
