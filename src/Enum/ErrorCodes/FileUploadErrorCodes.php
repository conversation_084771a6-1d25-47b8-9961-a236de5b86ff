<?php

namespace App\Enum\ErrorCodes;

enum FileUploadErrorCodes: string
{
    case FILE_UPLOAD_SERVICE_INVALID_FILE_TYPE = 'FILE_UPLOAD_SERVICE_INVALID_FILE_TYPE';
    case FILE_UPLOAD_SERVICE_FILE_TOO_LARGE = 'FILE_UPLOAD_SERVICE_FILE_TOO_LARGE';
    case FILE_UPLOAD_SERVICE_DIRECTORY_CREATION_FAILED = 'FILE_UPLOAD_SERVICE_DIRECTORY_CREATION_FAILED';
    case FILE_UPLOAD_SERVICE_UPLOAD_FAILED = 'FILE_UPLOAD_SERVICE_UPLOAD_FAILED';
    case FILE_UPLOAD_SERVICE_FILE_NOT_FOUND = 'FILE_UPLOAD_SERVICE_FILE_NOT_FOUND';
    case FILE_UPLOAD_SERVICE_ANDROID_CONTENT_URI = 'FILE_UPLOAD_SERVICE_ANDROID_CONTENT_URI';
    case FILE_UPLOAD_SERVICE_MIGRATION_FAILED = 'FILE_UPLOAD_SERVICE_MIGRATION_FAILED';
}
