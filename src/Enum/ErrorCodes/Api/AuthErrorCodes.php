<?php
namespace App\Enum\ErrorCodes\Api;

class AuthErrorCodes 
{
    public const AUTH_USER_NOT_FOUND_OR_ACTIVE = [
        'code' => 'AC-001',
        'message' => 'El usuario no existe o ya está activo.',
        'http_code' => 404,
    ];

    public const AUTH_COMPANY_NOT_FOUND_OR_INACTIVE = [
        'code' => 'AC-002',
        'message' => 'La empresa no existe o no está activa.',
        'http_code' => 404,
    ];

    public const AUTH_PHONE_VERIFICATION_FAILED = [
        'code' => 'AC-003',
        'message' => 'Falló el proceso de verificación del teléfono.',
        'http_code' => 500,
    ];

    public const AUTH_USER_NOT_FOUND_OR_INACTIVE = [
        'code' => 'AUTH_USER_NOT_FOUND_OR_INACTIVE',
        'message' => 'Usuario no encontrado o inactivo.',
        'http_code' => 404
    ];

    public const AUTH_USER_INCORRECT_PASSWORD = [
        'code' => 'AUTH_USER_INCORRECT_PASSWORD',
        'message' => 'Contraseña incorrecta.',
        'http_code' => 401
    ];

    public const AUTH_EMAIL_ALREADY_REGISTERED = [
        'code' => 'AC-006',
        'message' => 'El correo electrónico ya está registrado.',
        'http_code' => 409,
    ];

    public const AUTH_MISSING_PHONE_NUMBER = [
        'code' => 'AUTH_MISSING_PHONE_NUMBER',
        'message' => 'El número de teléfono es requerido.',
        'http_code' => 400
    ];

    public const AUTH_SMS_SENDING_FAILED = [
        'code' => 'AUTH_SMS_SENDING_FAILED',
        'message' => 'Error al enviar el código de verificación por SMS.',
        'http_code' => 500
    ];

    public const AUTH_INTERNAL_ERROR = [
        'code' => 'AUTH_INTERNAL_ERROR',
        'message' => 'Error interno del servidor.',
        'http_code' => 500
    ];

    public const AUTH_REGISTRATION_FAILED = [
        'code' => 'AC-007',
        'message' => 'Error durante el proceso de registro.',
        'http_code' => 500
    ];
}