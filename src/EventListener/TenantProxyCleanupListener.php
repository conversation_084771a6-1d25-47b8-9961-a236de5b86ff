<?php

namespace App\EventListener;

use App\Service\TenantManager;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * EventListener que limpia automáticamente los proxies cuando se cambia de tenant
 */
class TenantProxyCleanupListener implements EventSubscriberInterface
{
    private TenantManager $tenantManager;
    private ManagerRegistry $doctrine;
    private ?string $lastTenant = null;

    public function __construct(TenantManager $tenantManager, ManagerRegistry $doctrine)
    {
        $this->tenantManager = $tenantManager;
        $this->doctrine = $doctrine;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['onKernelRequest', 10],
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $dominio = $request->attributes->get('dominio');

        if (!$dominio || !$this->tenantManager->isValidTenant($dominio)) {
            return;
        }

        // Si el tenant ha cambiado, limpiar todos los entity managers
        if ($this->lastTenant !== null && $this->lastTenant !== $dominio) {
            $this->clearAllEntityManagers();
        }

        $this->lastTenant = $dominio;
    }

    /**
     * Limpia todos los Entity Managers para evitar problemas de proxy entre tenants
     */
    private function clearAllEntityManagers(): void
    {
        try {
            // Limpiar todos los entity managers configurados
            $entityManagers = ['default', 'ts', 'SNT'];
            
            foreach ($entityManagers as $emName) {
                try {
                    $em = $this->doctrine->getManager($emName);
                    if ($em->isOpen()) {
                        $em->clear();
                    }
                } catch (\Exception $e) {
                    // Ignorar errores de entity managers no disponibles
                    continue;
                }
            }
        } catch (\Exception $e) {
            // En caso de error, no hacer nada para no interrumpir la aplicación
        }
    }
}
