<?php

namespace App\Factory;

use App\Entity\Conversation;
use App\Entity\User;
use App\Repository\ConversationRepository;
use Doctrine\ORM\EntityManagerInterface;

class ConversationFactory
{

    public function __construct(
        private readonly ConversationRepository $conversationRepository,
    )
    {

    }

    public function create(User $sender): Conversation
    {
        $conversation = new Conversation();

        $conversation->addUser($sender);

        $this->conversationRepository->save($conversation);
        return $conversation;
    }
}