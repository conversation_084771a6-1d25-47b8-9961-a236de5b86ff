<?php
namespace App\Controller\Api;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use App\Service\ErrorResponseService;
use App\Service\RequestValidatorService;
use App\DTO\Beneficiary\BeneficiaryCreateRequest;
use App\DTO\Beneficiary\BeneficiaryUpdateRequest;
use App\Entity\Beneficiary;
use App\Enum\Status;
use App\Service\ImageUploadService;
use App\Enum\ErrorCodes\Api\BeneficiaryErrorCodes;
use App\Service\ImagePathService;
use App\Service\TenantManager;

#[Route('/{dominio}/api')]
class BeneficiaryController extends AbstractController
{
    private ErrorResponseService $errorResponseService;
    private ImageUploadService $imageUploadService;
    private TenantManager $tenantManager;
    private ImagePathService $imagePathService;

    public function __construct(
        ImageUploadService $imageUploadService,
        ErrorResponseService $errorResponseService,
        ImagePathService $imagePathService,
        TenantManager $tenantManager
    ) {
        $this->tenantManager = $tenantManager;
        $this->imageUploadService = $imageUploadService;
        $this->errorResponseService = $errorResponseService;
        $this->imagePathService = $imagePathService;
    }

    #[Route('/users/{userId}/beneficiary', name: 'api_beneficiary', methods: ['GET'])]
    public function getBeneficiary(string $dominio, int $userId): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $userExists = $em->getRepository(User::class)->findOneBy([
            'id' => $userId,
            'status' => Status::ACTIVE,
        ]);
        if (!$userExists) {
            return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_USER_NOT_FOUND_OR_INACTIVE,
                [
                    'user_id' => $userId,
                ]
            );
        }

        $beneficiaries = $userExists->getBeneficiaries()->filter(fn($beneficiary) => $beneficiary->getStatus() === Status::ACTIVE);
        if ($beneficiaries->isEmpty()) {
            return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_NO_ACTIVE_BENEFICIARIES_FOUND,
                [
                    'user_id' => $userId,
                ]
            );
        }

        $response = array_map(fn($beneficiary) => $this->mapBeneficiaryToArray($beneficiary), $beneficiaries->toArray());

        return new JsonResponse([
            'message' => 'Beneficiarios encontrados correctamente.',
            'beneficiaries' => $response,
            'code' => 200,
        ], 200);
    }

    #[Route('/users/{userId}/beneficiary', name: 'api_beneficiary_create', methods: ['POST'])]
    public function createBeneficiary(string $dominio, int $userId, Request $request, RequestValidatorService $requestValidator): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            $beneficiaryCreateRequest = $requestValidator->validateAndMap($request, BeneficiaryCreateRequest::class);
            if ($beneficiaryCreateRequest instanceof JsonResponse) {
                return $beneficiaryCreateRequest;
            }

            $user = $em->getRepository(User::class)->findOneBy([
                'id' => $userId,
                'status' => Status::ACTIVE,
            ]);
            if (!$user) {
                return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_CREATE_USER_NOT_FOUND_OR_INACTIVE,
                    [
                        'user_id' => $userId,
                    ]
                );
            }

            $existingBeneficiary = $em->getRepository(Beneficiary::class)->findOneBy([
                'curp' => $beneficiaryCreateRequest->curp,
                'status' => Status::ACTIVE,
            ]);
            if ($existingBeneficiary) {
                return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_CREATE_CURP_ALREADY_EXISTS,
                    [
                        'curp' => $beneficiaryCreateRequest->curp,
                    ]
                );
            }

            //TODO: Validate if beneficiary already exists but is inactive.
            $beneficiary = new Beneficiary();
            $beneficiary->setCurp($beneficiaryCreateRequest->curp);
            $beneficiary->setName($beneficiaryCreateRequest->name);

            // Combinar apellido paterno y materno
            $fullLastName = $beneficiaryCreateRequest->last_name;
            if ($beneficiaryCreateRequest->maternal_last_name) {
                $fullLastName .= ' ' . $beneficiaryCreateRequest->maternal_last_name;
            }
            $beneficiary->setLastName($fullLastName);

            $beneficiary->setKinship($beneficiaryCreateRequest->kinship);
            $beneficiary->setGender($beneficiaryCreateRequest->gender);
            $beneficiary->setEducation($beneficiaryCreateRequest->education);
            $beneficiary->setBirthday(new \DateTimeImmutable($beneficiaryCreateRequest->birthday));
            $beneficiary->setStatus(Status::ACTIVE);
            $beneficiary->setCreatedAt(new \DateTimeImmutable());
            $beneficiary->setUpdatedAt(new \DateTimeImmutable());

            // Usar la entidad User directamente (ya está gestionada por el EntityManager)
            $beneficiary->setUser($user);

            // Guardar primero para obtener el ID
            $em->persist($beneficiary);
            $em->flush();

            // Actualizar lastSeen del usuario directamente en BD para evitar problemas de cascade
            $em->createQueryBuilder()
                ->update(User::class, 'u')
                ->set('u.last_seen', ':lastSeen')
                ->where('u.id = :userId')
                ->setParameter('lastSeen', new \DateTimeImmutable())
                ->setParameter('userId', $user->getId())
                ->getQuery()
                ->execute();

            // Ahora procesar la foto con el ID del beneficiario
            $photoPath = $this->handleBeneficiaryImage(null, $request->files->get('photo'), BeneficiaryErrorCodes::BENEFICIARY_CREATE_PHOTO_UPLOAD_FAILED, $user->getId(), $beneficiary->getId());

            if ($photoPath) {
                $beneficiary->setPhoto($photoPath);
                $em->flush(); // Guardar la ruta de la foto
            }

            // Devolver los datos completos del beneficiario creado incluyendo la foto
            return new JsonResponse([
                'message' => 'Beneficiario creado correctamente.',
                'beneficiary' => $this->mapBeneficiaryToArray($beneficiary),
                'code' => 201,
            ], 201);
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('Beneficiary creation error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

            return new JsonResponse([
                'code' => 500,
                'error' => 'Error interno del sistema',
                'message' => 'Se ha producido un error temporal. Por favor intente nuevamente.',
                'debug' => $e->getMessage() // Temporal para debugging
            ], 500);
        }
    }

    #[Route('/users/{userId}/beneficiary/{beneficiaryId}', name: 'api_beneficiary_update', methods: ['POST'])]
    public function updateBeneficiary(string $dominio, int $userId, int $beneficiaryId, Request $request, RequestValidatorService $requestValidator): JsonResponse
    {
        error_log('🚀 BENEFICIARY UPDATE ENDPOINT CALLED!');
        error_log('🚀 Dominio: ' . $dominio);
        error_log('🚀 UserId: ' . $userId);
        error_log('🚀 BeneficiaryId: ' . $beneficiaryId);
        error_log('🚀 Request Method: ' . $request->getMethod());
        error_log('🚀 Content-Type: ' . $request->headers->get('Content-Type'));
        error_log('🚀 Request URI: ' . $request->getRequestUri());

        try {
            error_log('📸 Beneficiary Update: Iniciando actualización de beneficiario');
            error_log('📸 Beneficiary Update: Content-Type: ' . $request->headers->get('Content-Type'));
            error_log('📸 Beneficiary Update: Método HTTP: ' . $request->getMethod());

            // Verificar si el tenant ya está establecido para evitar limpiar el EntityManager
            $currentTenant = $this->tenantManager->getCurrentTenant();
            if ($currentTenant !== $dominio) {
                error_log('📸 Beneficiary Update: Cambiando tenant de ' . ($currentTenant ?? 'null') . ' a ' . $dominio);
                $this->tenantManager->setCurrentTenant($dominio);
            } else {
                error_log('📸 Beneficiary Update: Tenant ya establecido: ' . $dominio);
            }
            $em = $this->tenantManager->getEntityManager();

            $beneficiaryUpdateRequest = $requestValidator->validateAndMap($request, BeneficiaryUpdateRequest::class);
            if ($beneficiaryUpdateRequest instanceof JsonResponse) {
                return $beneficiaryUpdateRequest;
            }

            $user = $em->getRepository(User::class)->findOneBy([
                'id' => $userId,
                'status' => Status::ACTIVE,
            ]);
            if (!$user) {
                return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_UPDATE_USER_NOT_FOUND_OR_INACTIVE,
                    [
                        'user_id' => $userId,
                    ]
                );
            }

            // Obtener el beneficiario directamente del repositorio para evitar problemas de cascade
            $beneficiary = $em->getRepository(Beneficiary::class)->findOneBy([
                'id' => $beneficiaryId,
                'user' => $user,
                'status' => Status::ACTIVE,
            ]);
            if (!$beneficiary) {
                return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_UPDATE_BENEFICIARY_NOT_FOUND_OR_INACTIVE,
                    [
                        'user_id' => $userId,
                        'beneficiary_id' => $beneficiaryId,
                    ]
                );
            }

            // Manejar la foto solo si hay un archivo nuevo
            $uploadedFile = $request->files->get('photo');
            $photoPath = null;

            // Debug: Mostrar todos los archivos recibidos
            error_log('📸 Beneficiary Update: Archivos recibidos en request: ' . json_encode(array_keys($request->files->all())));
            error_log('📸 Beneficiary Update: Contenido de $_FILES: ' . json_encode(array_keys($_FILES)));
            error_log('📸 Beneficiary Update: uploadedFile es: ' . ($uploadedFile ? 'VÁLIDO (' . $uploadedFile->getClientOriginalName() . ')' : 'NULL'));

            if ($uploadedFile) {
                error_log('📸 Beneficiary Update: Archivo de foto recibido: ' . $uploadedFile->getClientOriginalName());
                error_log('📸 Beneficiary Update: Tamaño del archivo: ' . $uploadedFile->getSize() . ' bytes');
                error_log('📸 Beneficiary Update: Tipo MIME: ' . $uploadedFile->getMimeType());
                error_log('📸 Beneficiary Update: Beneficiario ID: ' . $beneficiary->getId() . ', Usuario ID: ' . $user->getId());

                try {
                    $photoPath = $this->handleBeneficiaryImage($beneficiary->getPhoto(), $uploadedFile, BeneficiaryErrorCodes::BENEFICIARY_UPDATE_PHOTO_UPLOAD_FAILED, $user->getId(), $beneficiary->getId());
                    error_log('📸 Beneficiary Update: Ruta de foto generada: ' . ($photoPath ?? 'NULL'));
                } catch (\Exception $e) {
                    error_log('❌ Beneficiary Update: Error en handleBeneficiaryImage: ' . $e->getMessage());
                    throw $e;
                }
            } else {
                error_log('📸 Beneficiary Update: No se recibió archivo de foto');
            }

            $hasChanged = false;
            $this->updateFieldIfChanged($beneficiary, 'setCurp', $beneficiaryUpdateRequest->curp, $beneficiary->getCurp(), $hasChanged);
            $this->updateFieldIfChanged($beneficiary, 'setName', $beneficiaryUpdateRequest->name, $beneficiary->getName(), $hasChanged);
            // Combinar apellido paterno y materno para la actualización
            $fullLastName = $beneficiaryUpdateRequest->last_name;
            if ($beneficiaryUpdateRequest->maternal_last_name) {
                $fullLastName .= ' ' . $beneficiaryUpdateRequest->maternal_last_name;
            }
            $this->updateFieldIfChanged($beneficiary, 'setLastName', $fullLastName, $beneficiary->getLastName(), $hasChanged);
            $this->updateFieldIfChanged($beneficiary, 'setKinship', $beneficiaryUpdateRequest->kinship, $beneficiary->getKinship(), $hasChanged);
            $this->updateFieldIfChanged($beneficiary, 'setGender', $beneficiaryUpdateRequest->gender, $beneficiary->getGender(), $hasChanged);
            $this->updateFieldIfChanged($beneficiary, 'setEducation', $beneficiaryUpdateRequest->education, $beneficiary->getEducation(), $hasChanged);

            // Solo actualizar la foto si hay un archivo nuevo
            if ($photoPath !== null) {
                error_log('📸 Beneficiary Update: Actualizando foto en BD. Ruta anterior: ' . ($beneficiary->getPhoto() ?? 'NULL') . ', Nueva ruta: ' . $photoPath);
                $beneficiary->setPhoto($photoPath);
                $hasChanged = true;
                error_log('📸 Beneficiary Update: Foto actualizada en entidad. Verificando: ' . ($beneficiary->getPhoto() ?? 'NULL'));
            } else if ($uploadedFile) {
                // Si había un archivo pero $photoPath es null, hay un error
                error_log('❌ Beneficiary Update: ERROR - Se recibió archivo pero photoPath es null');
                error_log('❌ Beneficiary Update: uploadedFile válido: ' . ($uploadedFile->isValid() ? 'SÍ' : 'NO'));
                error_log('❌ Beneficiary Update: uploadedFile nombre: ' . $uploadedFile->getClientOriginalName());
                return new JsonResponse([
                    'code' => 500,
                    'error' => 'Error al procesar la imagen',
                    'message' => 'La imagen no se pudo procesar correctamente'
                ], 500);
            }
            $this->updateFieldIfChanged(
                $beneficiary,
                'setBirthday',
                $beneficiaryUpdateRequest->birthday ? new \DateTime($beneficiaryUpdateRequest->birthday) : null,
                $beneficiary->getBirthday(),
                $hasChanged
            );

            if (!$hasChanged) {
                return new JsonResponse([
                    'message' => 'No se realizaron cambios.',
                    'code' => 200,
                ], 200);
            }

            $beneficiary->setUpdatedAt(new \DateTimeImmutable());

            // Log antes del flush
            error_log('📸 Beneficiary Update: Antes del flush - Foto en entidad: ' . ($beneficiary->getPhoto() ?? 'NULL'));
            error_log('📸 Beneficiary Update: hasChanged: ' . ($hasChanged ? 'true' : 'false'));

            // Asegurar que el beneficiario esté gestionado antes del flush
            error_log('📸 Beneficiary Update: Estado del beneficiario antes de persist: ' . ($em->contains($beneficiary) ? 'MANAGED' : 'NOT MANAGED'));
            $em->persist($beneficiary);
            error_log('📸 Beneficiary Update: Estado del beneficiario después de persist: ' . ($em->contains($beneficiary) ? 'MANAGED' : 'NOT MANAGED'));

            // Flush de los cambios del beneficiario
            try {
                $em->flush();

                // Actualizar lastSeen del usuario directamente en BD para evitar problemas de cascade
                $em->createQueryBuilder()
                    ->update(User::class, 'u')
                    ->set('u.last_seen', ':lastSeen')
                    ->where('u.id = :userId')
                    ->setParameter('lastSeen', new \DateTimeImmutable())
                    ->setParameter('userId', $user->getId())
                    ->getQuery()
                    ->execute();
                error_log('📸 Beneficiary Update: Flush ejecutado exitosamente');

                // Verificar inmediatamente en BD
                $em->refresh($beneficiary);
                error_log('📸 Beneficiary Update: Después del refresh - Foto en BD: ' . ($beneficiary->getPhoto() ?? 'NULL'));
                error_log('📸 Beneficiary Update: Después del refresh - Updated_at: ' . ($beneficiary->getUpdatedAt() ? $beneficiary->getUpdatedAt()->format('Y-m-d H:i:s') : 'NULL'));

            } catch (\Exception $flushError) {
                error_log('❌ Beneficiary Update: ERROR en flush: ' . $flushError->getMessage());
                error_log('❌ Beneficiary Update: Stack trace: ' . $flushError->getTraceAsString());
                throw $flushError;
            }

            error_log('📸 Beneficiary Update: Datos guardados en BD. Foto final: ' . ($beneficiary->getPhoto() ?? 'NULL'));

            // Devolver los datos actualizados del beneficiario incluyendo la foto
            return new JsonResponse([
                'message' => 'Beneficiario actualizado correctamente.',
                'beneficiary' => $this->mapBeneficiaryToArray($beneficiary),
                'code' => 200,
            ], 200);
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('Beneficiary update error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

            return new JsonResponse([
                'code' => 500,
                'error' => 'Error interno del sistema',
                'message' => 'Se ha producido un error temporal. Por favor intente nuevamente.',
                'debug' => $e->getMessage() // Temporal para debugging
            ], 500);
        }
    }

    #[Route('/users/{userId}/beneficiary/{beneficiaryId}/photo', name: 'api_beneficiary_update_photo', methods: ['POST'])]
    public function updateBeneficiaryPhoto(string $dominio, int $userId, int $beneficiaryId, Request $request): JsonResponse
    {
        error_log('🚀 BENEFICIARY UPDATE PHOTO ENDPOINT CALLED!');
        error_log('🚀 Dominio: ' . $dominio);
        error_log('🚀 UserId: ' . $userId);
        error_log('🚀 BeneficiaryId: ' . $beneficiaryId);
        error_log('🚀 Request Method: ' . $request->getMethod());
        error_log('🚀 Content-Type: ' . $request->headers->get('Content-Type'));

        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            // Buscar el usuario
            $user = $em->getRepository(User::class)->findOneBy([
                'id' => $userId,
                'status' => Status::ACTIVE,
            ]);

            if (!$user) {
                error_log('❌ Usuario no encontrado: ' . $userId);
                return new JsonResponse([
                    'code' => 404,
                    'error' => 'Usuario no encontrado',
                    'message' => 'El usuario especificado no existe o está inactivo'
                ], 404);
            }

            // Buscar el beneficiario directamente desde el repositorio
            $beneficiary = $em->getRepository(Beneficiary::class)->findOneBy([
                'id' => $beneficiaryId,
                'user' => $user,
                'status' => Status::ACTIVE,
            ]);

            if (!$beneficiary) {
                error_log('❌ Beneficiario no encontrado: ' . $beneficiaryId);
                return new JsonResponse([
                    'code' => 404,
                    'error' => 'Beneficiario no encontrado',
                    'message' => 'El beneficiario especificado no existe o está inactivo'
                ], 404);
            }

            error_log('✅ Beneficiario encontrado: ' . $beneficiary->getName() . ' ' . $beneficiary->getLastName());
            error_log('📸 Foto actual: ' . ($beneficiary->getPhoto() ?? 'NULL'));

            // Verificar que se envió una foto
            $uploadedFile = $request->files->get('photo');
            if (!$uploadedFile) {
                error_log('❌ No se recibió archivo de foto');
                return new JsonResponse([
                    'code' => 400,
                    'error' => 'Foto requerida',
                    'message' => 'Debe enviar una foto para actualizar'
                ], 400);
            }

            error_log('📸 Archivo recibido: ' . $uploadedFile->getClientOriginalName());
            error_log('📸 Tamaño: ' . $uploadedFile->getSize() . ' bytes');
            error_log('📸 Tipo MIME: ' . $uploadedFile->getMimeType());

            // Procesar la foto
            $photoPath = $this->handleBeneficiaryImage(
                $beneficiary->getPhoto(),
                $uploadedFile,
                BeneficiaryErrorCodes::BENEFICIARY_UPDATE_PHOTO_UPLOAD_FAILED,
                $user->getId(),
                $beneficiary->getId()
            );

            if (!$photoPath) {
                error_log('❌ Error al procesar la foto');
                return new JsonResponse([
                    'code' => 500,
                    'error' => 'Error al procesar la foto',
                    'message' => 'No se pudo guardar la imagen'
                ], 500);
            }

            error_log('✅ Foto procesada exitosamente: ' . $photoPath);

            // Actualizar solo la foto y timestamp
            $beneficiary->setPhoto($photoPath);
            $beneficiary->setUpdatedAt(new \DateTimeImmutable());

            error_log('📸 Antes del flush - Foto en entidad: ' . $beneficiary->getPhoto());

            // Asegurar que el beneficiario esté gestionado antes del flush
            $em->persist($beneficiary);

            // Guardar cambios del beneficiario
            $em->flush();

            // Actualizar lastSeen del usuario directamente en BD para evitar problemas de cascade
            $em->createQueryBuilder()
                ->update(User::class, 'u')
                ->set('u.last_seen', ':lastSeen')
                ->where('u.id = :userId')
                ->setParameter('lastSeen', new \DateTimeImmutable())
                ->setParameter('userId', $user->getId())
                ->getQuery()
                ->execute();

            error_log('✅ Flush completado');

            // Verificar que se guardó
            $em->refresh($beneficiary);
            error_log('📸 Después del refresh - Foto en BD: ' . ($beneficiary->getPhoto() ?? 'NULL'));
            error_log('📸 Después del refresh - Updated_at: ' . $beneficiary->getUpdatedAt()->format('Y-m-d H:i:s'));

            return new JsonResponse([
                'message' => 'Foto del beneficiario actualizada correctamente',
                'photo_path' => $photoPath,
                'beneficiary' => [
                    'id' => $beneficiary->getId(),
                    'name' => $beneficiary->getName(),
                    'last_name' => $beneficiary->getLastName(),
                    'photo' => $beneficiary->getPhoto(),
                    'updated_at' => $beneficiary->getUpdatedAt()->format('Y-m-d H:i:s')
                ],
                'code' => 200,
            ], 200);

        } catch (\Exception $e) {
            error_log('❌ Error en updateBeneficiaryPhoto: ' . $e->getMessage());
            error_log('❌ Stack trace: ' . $e->getTraceAsString());

            return new JsonResponse([
                'code' => 500,
                'error' => 'Error interno del servidor',
                'message' => 'Error al actualizar la foto: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/users/{userId}/beneficiary/{beneficiaryId}', name: 'api_beneficiary_delete', methods: ['DELETE'])]
    public function deleteBeneficiary(string $dominio, int $userId, int $beneficiaryId): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $user = $em->getRepository(User::class)->findOneBy([
            'id' => $userId,
            'status' => Status::ACTIVE,
        ]);
        if (!$user) {
            return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_DELETE_USER_NOT_FOUND_OR_INACTIVE,
                [
                    'user_id' => $userId,
                ]
            );
        }

        // Obtener el beneficiario directamente del repositorio para evitar problemas de cascade
        $beneficiary = $em->getRepository(Beneficiary::class)->findOneBy([
            'id' => $beneficiaryId,
            'user' => $user,
            'status' => Status::ACTIVE,
        ]);
        if (!$beneficiary) {
            return $this->errorResponseService->createErrorResponse(BeneficiaryErrorCodes::BENEFICIARY_DELETE_BENEFICIARY_NOT_FOUND_OR_INACTIVE,
                [
                    'user_id' => $userId,
                    'beneficiary_id' => $beneficiaryId,
                ]
            );
        }

        $beneficiary->setStatus(Status::DELETED);
        $beneficiary->setUpdatedAt(new \DateTimeImmutable());

        $em->persist($beneficiary);
        $em->flush();

        // Actualizar lastSeen del usuario directamente en BD para evitar problemas de cascade
        $em->createQueryBuilder()
            ->update(User::class, 'u')
            ->set('u.last_seen', ':lastSeen')
            ->where('u.id = :userId')
            ->setParameter('lastSeen', new \DateTimeImmutable())
            ->setParameter('userId', $user->getId())
            ->getQuery()
            ->execute();

        return new JsonResponse([
            'message' => 'Beneficiario eliminado correctamente.',
            'code' => 200,
        ], 200);
    }

    private function updateFieldIfChanged($entity, string $setter, $newValue, $currentValue, &$hasChanged): void
    {
        if ($newValue !== null && $newValue !== $currentValue) {
            $entity->$setter($newValue);
            $hasChanged = true;
        }
    }

    private function mapBeneficiaryToArray(Beneficiary $beneficiary): array
    {
        return [
            'id' => $beneficiary->getId(),
            'curp' => $beneficiary->getCurp(),
            'name' => $beneficiary->getName(),
            'last_name' => $beneficiary->getLastName(),
            'kinship' => $beneficiary->getKinship(),
            'photo' => $this->imagePathService->generateFullPath($beneficiary->getPhoto()),
            'birthday' => $beneficiary->getBirthday() ? $beneficiary->getBirthday()->format('Y-m-d') : null,
            'gender' => $beneficiary->getGender(),
            'education' => $beneficiary->getEducation(),
        ];
    }

    private function handleBeneficiaryImage(?string $oldPhotoPath, $uploadFile, $errorCode, int $userId, ?int $beneficiaryId = null): ?string
    {
        if (!$uploadFile) {
            error_log('📸 handleBeneficiaryImage: No hay archivo, devolviendo ruta anterior: ' . ($oldPhotoPath ?? 'NULL'));
            return $oldPhotoPath;
        }

        // Validar que el archivo sea válido
        if (!$uploadFile->isValid()) {
            error_log('❌ handleBeneficiaryImage: Archivo no válido: ' . $uploadFile->getErrorMessage());
            throw new \Exception('Archivo no válido: ' . $uploadFile->getErrorMessage());
        }

        try {
            error_log('📸 handleBeneficiaryImage: Procesando archivo: ' . $uploadFile->getClientOriginalName());
            error_log('📸 handleBeneficiaryImage: Tamaño: ' . $uploadFile->getSize() . ' bytes');
            error_log('📸 handleBeneficiaryImage: Tipo MIME: ' . $uploadFile->getMimeType());

            // Eliminar foto anterior solo si el upload es exitoso
            $imagePath = $this->imageUploadService->uploadImage($uploadFile, 'beneficiaries', $userId, $beneficiaryId);
            error_log('📸 handleBeneficiaryImage: Resultado del upload: ' . ($imagePath ?? 'NULL'));

            if ($imagePath === null) {
                error_log('❌ handleBeneficiaryImage: ImageUploadService devolvió null');
                throw new \Exception('Error al subir la imagen del beneficiario: ' . $uploadFile->getClientOriginalName());
            }

            // Solo eliminar la foto anterior si el nuevo upload fue exitoso
            if ($oldPhotoPath && $oldPhotoPath !== $imagePath) {
                $absoluteOldPhotoPath = $this->getParameter('uploads_directory') . '/' . $oldPhotoPath;
                error_log('📸 handleBeneficiaryImage: Eliminando foto anterior: ' . $absoluteOldPhotoPath);
                if (file_exists($absoluteOldPhotoPath)) {
                    unlink($absoluteOldPhotoPath);
                    error_log('📸 handleBeneficiaryImage: Foto anterior eliminada');
                }
            }

            error_log('📸 handleBeneficiaryImage: Imagen subida exitosamente: ' . $imagePath);
            error_log('📸 handleBeneficiaryImage: Retornando ruta: ' . $imagePath);
            return $imagePath;
        } catch (\Exception $e) {
            error_log('📸 handleBeneficiaryImage: Error: ' . $e->getMessage());
            throw new \Exception('Error al subir la imagen del beneficiario: ' . $e->getMessage());
        }
    }

    #[Route('/beneficiaries', name: 'api_beneficiaries_list', methods: ['GET'])]
    public function list(string $dominio): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $beneficiaries = $em->getRepository(Beneficiary::class)->findAll();

        $data = [];
        foreach ($beneficiaries as $beneficiary) {
            $data[] = [
                'id' => $beneficiary->getId(),
                'name' => $beneficiary->getName(),
                'relationship' => $beneficiary->getRelationship(),
                'birthdate' => $beneficiary->getBirthdate()->format('Y-m-d'),
                'user' => $beneficiary->getUser()->getId()
            ];
        }

        return $this->json($data);
    }

    #[Route('/beneficiaries/{id}', name: 'api_beneficiaries_show', methods: ['GET'])]
    public function show(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $beneficiary = $em->getRepository(Beneficiary::class)->find($id);

        if (!$beneficiary) {
            return $this->json(['error' => 'Beneficiario no encontrado'], 404);
        }

        return $this->json([
            'id' => $beneficiary->getId(),
            'name' => $beneficiary->getName(),
            'relationship' => $beneficiary->getRelationship(),
            'birthdate' => $beneficiary->getBirthdate()->format('Y-m-d'),
            'user' => $beneficiary->getUser()->getId()
        ]);
    }

    #[Route('/beneficiaries', name: 'api_beneficiaries_create', methods: ['POST'])]
    public function create(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $data = json_decode($request->getContent(), true);

        $beneficiary = new Beneficiary();
        $beneficiary->setName($data['name']);
        $beneficiary->setRelationship($data['relationship']);
        $beneficiary->setBirthdate(new \DateTime($data['birthdate']));

        // Asumiendo que el usuario está autenticado
        $beneficiary->setUser($this->getUser());

        $em->persist($beneficiary);
        $em->flush();

        return $this->json([
            'id' => $beneficiary->getId(),
            'name' => $beneficiary->getName(),
            'relationship' => $beneficiary->getRelationship(),
            'birthdate' => $beneficiary->getBirthdate()->format('Y-m-d'),
            'user' => $beneficiary->getUser()->getId()
        ], 201);
    }

    #[Route('/beneficiaries/{id}', name: 'api_beneficiaries_update', methods: ['PUT'])]
    public function update(string $dominio, int $id, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $beneficiary = $em->getRepository(Beneficiary::class)->find($id);

        if (!$beneficiary) {
            return $this->json(['error' => 'Beneficiario no encontrado'], 404);
        }

        $data = json_decode($request->getContent(), true);

        if (isset($data['name'])) {
            $beneficiary->setName($data['name']);
        }
        if (isset($data['relationship'])) {
            $beneficiary->setRelationship($data['relationship']);
        }
        if (isset($data['birthdate'])) {
            $beneficiary->setBirthdate(new \DateTime($data['birthdate']));
        }

        $em->flush();

        return $this->json([
            'id' => $beneficiary->getId(),
            'name' => $beneficiary->getName(),
            'relationship' => $beneficiary->getRelationship(),
            'birthdate' => $beneficiary->getBirthdate()->format('Y-m-d'),
            'user' => $beneficiary->getUser()->getId()
        ]);
    }

    #[Route('/beneficiaries/{id}', name: 'api_beneficiaries_delete', methods: ['DELETE'])]
    public function delete(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $beneficiary = $em->getRepository(Beneficiary::class)->find($id);

        if (!$beneficiary) {
            return $this->json(['error' => 'Beneficiario no encontrado'], 404);
        }

        $em->remove($beneficiary);
        $em->flush();

        return $this->json(null, 204);
    }
}