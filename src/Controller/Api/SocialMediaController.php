<?php
namespace App\Controller\Api;

use App\Entity\Company;
use App\Entity\SocialMedia;
use App\Service\ErrorResponseService;
use App\Service\RequestValidatorService;
use App\Service\TenantManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Annotation\Route;
use App\DTO\SocialMediaGetRequest;
use App\Enum\Status;
use App\Enum\ErrorCodes\Api\SocialMediaErrorCodes;
use Doctrine\Common\Collections\Collection;
use App\Service\ImagePathService;

#[Route('/{dominio}/api')]
class SocialMediaController extends AbstractController
{
    private TenantManager $tenantManager;
    private ErrorResponseService $errorResponseService;
    private RequestValidatorService $requestValidatorService;
    private ImagePathService $imagePathService;
    private RequestStack $requestStack;

    public function __construct(
        TenantManager $tenantManager,
        ErrorResponseService $errorResponseService,
        RequestValidatorService $requestValidatorService,
        ImagePathService $imagePathService,
        RequestStack $requestStack
    ) {
        $this->tenantManager = $tenantManager;
        $this->errorResponseService = $errorResponseService;
        $this->requestValidatorService = $requestValidatorService;
        $this->imagePathService = $imagePathService;
        $this->requestStack = $requestStack;
    }

    /**
     * @throws \Exception
     */
    #[Route('/social-media', name: 'api_social_media_list', methods: ['GET'])]
    public function getSocialMedia(string $dominio, Request $request): JsonResponse
    {
        try {
            error_log("=== Iniciando getSocialMedia ===");
            error_log("Dominio: " . $dominio);
            error_log("Request params: " . json_encode($request->query->all()));

            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            $socialMediaGetRequest = $this->requestValidatorService->validateAndMap($request, SocialMediaGetRequest::class, true);

            // Convertir company_id de string a integer si es necesario
            if ($socialMediaGetRequest->company_id && is_string($socialMediaGetRequest->company_id)) {
                $socialMediaGetRequest->company_id = (int) $socialMediaGetRequest->company_id;
            }

            error_log("Request validado: " . json_encode([
                    'start_date' => $socialMediaGetRequest->start_date,
                    'end_date' => $socialMediaGetRequest->end_date,
                    'company_id' => $socialMediaGetRequest->company_id,
                ]));

            if ($socialMediaGetRequest instanceof JsonResponse) {
                return $socialMediaGetRequest;
            }

            if (($socialMediaGetRequest->start_date !== null && $socialMediaGetRequest->end_date === null) ||
                ($socialMediaGetRequest->start_date === null && $socialMediaGetRequest->end_date !== null)) {
                return $this->errorResponseService->createErrorResponse(SocialMediaErrorCodes::SOCIAL_MEDIA_INVALID_DATE_RANGE,
                    [
                        'start_date' => $socialMediaGetRequest->start_date,
                        'end_date' => $socialMediaGetRequest->end_date,
                    ]
                );
            }

            $company = $em->getRepository(Company::class)->findOneBy([
                'id' => $socialMediaGetRequest->company_id,
                'status' => Status::ACTIVE,
            ]);
            if (!$company) {
                return $this->errorResponseService->createErrorResponse(SocialMediaErrorCodes::SOCIAL_MEDIA_COMPANY_NOT_FOUND_OR_INACTIVE,
                    [
                        'company_id' => $socialMediaGetRequest->company_id,
                    ]
                );
            }

            // Obtener posts de la empresa filtrados por status
            $statusFilter = $socialMediaGetRequest->status ? Status::from($socialMediaGetRequest->status) : Status::ACTIVE;
            $filteredSocialMedia = $company->getSocialMedia()->filter(function($socialMedia) use ($statusFilter) {
                return $socialMedia->getStatus() === $statusFilter;
            })->toArray();

            // Aplicar filtros de fecha si están presentes
            $filteredSocialMedia = $this->filterSocialMedia($filteredSocialMedia, $socialMediaGetRequest);

            if (empty($filteredSocialMedia)) {
                // En lugar de devolver error, devolver respuesta exitosa con array vacío
                return new JsonResponse([
                    'social_media' => [],
                    'code' => 200,
                    'message' => 'No se encontraron publicaciones de redes sociales para los criterios especificados.'
                ], 200);
            }

            if ($socialMediaGetRequest->amount !== null) {
                $filteredSocialMedia = array_slice($filteredSocialMedia, 0, $socialMediaGetRequest->amount);
            }

            $response = array_map(fn($post) => $this->mapSocialMediaToArray($post), $filteredSocialMedia);

            return new JsonResponse([
                'social_media' => $response,
                'code' => 200,
            ], 200);
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('Social media error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());

            return new JsonResponse([
                'code' => 500,
                'error' => 'Error interno del sistema',
                'message' => 'Se ha producido un error temporal. Por favor intente nuevamente.',
                'debug' => $e->getMessage() // Temporal para debugging
            ], 500);
        }
    }

    #[Route('/social-media/{id}', name: 'api_social_media_show', methods: ['GET'])]
    public function show(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $socialMedia = $em->getRepository(SocialMedia::class)->find($id);

        if (!$socialMedia) {
            return $this->json(['error' => 'Red social no encontrada'], 404);
        }



        return $this->json([
            'id' => $socialMedia->getId(),
            'name' => $socialMedia->getTitle(),
            'url' => $socialMedia->getUrl(),
            'icon' => $socialMedia->getIcon(),
            'active' => $socialMedia->isActive()
        ]);
    }

    #[Route('/social-media', name: 'api_social_media_create', methods: ['POST'])]
    public function create(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $data = json_decode($request->getContent(), true);

        $socialMedia = new SocialMedia();
        $socialMedia->setTitle($data['name']);
        $socialMedia->setUrl($data['url']);
        $socialMedia->setImage($data['icon'] ?? null);
        $socialMedia->setStatus($data['active'] ?? true);

        $em->persist($socialMedia);
        $em->flush();

        return $this->json([
            'id' => $socialMedia->getId(),
            'name' => $socialMedia->getTitle(),
            'url' => $socialMedia->getUrl(),
            'icon' => $socialMedia->getImage(),
            'active' => $socialMedia->getStatus() === Status::ACTIVE
        ], 201);
    }

    #[Route('/social-media/{id}', name: 'api_social_media_update', methods: ['PUT'])]
    public function update(string $dominio, int $id, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $socialMedia = $em->getRepository(SocialMedia::class)->find($id);

        if (!$socialMedia) {
            return $this->json(['error' => 'Red social no encontrada'], 404);
        }

        $data = json_decode($request->getContent(), true);

        if (isset($data['name'])) {
            $socialMedia->setName($data['name']);
        }
        if (isset($data['url'])) {
            $socialMedia->setUrl($data['url']);
        }
        if (isset($data['icon'])) {
            $socialMedia->setIcon($data['icon']);
        }
        if (isset($data['active'])) {
            $socialMedia->setActive($data['active']);
        }

        $em->flush();

        return $this->json([
            'id' => $socialMedia->getId(),
            'name' => $socialMedia->getTitle(),
            'url' => $socialMedia->getUrl(),
            'icon' => $socialMedia->getImage(),
            'active' => $socialMedia->getStatus() === Status::ACTIVE
        ]);
    }

    #[Route('/social-media/{id}', name: 'api_social_media_delete', methods: ['DELETE'])]
    public function delete(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $socialMedia = $em->getRepository(SocialMedia::class)->find($id);

        if (!$socialMedia) {
            return $this->json(['error' => 'Red social no encontrada'], 404);
        }

        $em->remove($socialMedia);
        $em->flush();

        return $this->json(null, 204);
    }

    /**
     * @throws \Exception
     */
    private function filterSocialMedia($socialMediaPosts, SocialMediaGetRequest $request): array
    {
        if (!$request->start_date || !$request->end_date) {
            return $socialMediaPosts;
        }

        $startDate = new \DateTime($request->start_date . ' 00:00:00');
        $endDate = new \DateTime($request->end_date . ' 23:59:59');

        return array_filter($socialMediaPosts, function($post) use ($startDate, $endDate) {
            $postStart = new \DateTime($post->getStartDate()->format('Y-m-d') . ' 00:00:00');
            $postEnd = new \DateTime($post->getEndDate()->format('Y-m-d') . ' 23:59:59');


            return $postStart <= $endDate && $postEnd >= $startDate;
        });
    }



    private function mapSocialMediaToArray($post): array
    {
        return [
            'id' => $post->getId(),
            'title' => $post->getTitle(),
            'description' => $post->getDescription(),
            'image' => $this->imagePathService->generateFullPath($post->getImage()),
            'url' => $post->getUrl(),
            'platform' => $post->getPlatform(),
            'start_date' => $post->getStartDate()->format('Y-m-d H:i:s'),
            'end_date' => $post->getEndDate()->format('Y-m-d H:i:s'),
        ];
    }
}