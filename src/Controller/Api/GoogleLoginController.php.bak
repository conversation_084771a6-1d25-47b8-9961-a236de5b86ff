/*
ESTE ARCHIVO ESTÁ TEMPORALMENTE DESACTIVADO
Última modificación: <?php echo date('Y-m-d H:i:s'); ?>

<?php
namespace App\Controller\Api;

use App\Entity\User;
use App\Enum\Status;
use App\Service\ErrorResponseService;
use App\Enum\ErrorCodes\Api\GoogleAuthErrorCodes;
use App\Service\RequestValidatorService;
use Doctrine\ORM\EntityManagerInterface;
use Google_Client;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use App\Service\TenantManager;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;

#[Route('/{dominio}/api')]
class GoogleLoginController extends AbstractController
{
    private TenantManager $tenantManager;
    private ErrorResponseService $errorResponseService;
    private RequestValidatorService $requestValidatorService;
    private GoogleOAuthService $googleOAuthService;
    private JWTTokenManagerInterface $jwtTokenManager;

    public function __construct(
        TenantManager $tenantManager,
        ErrorResponseService $errorResponseService,
        RequestValidatorService $requestValidatorService,
        GoogleOAuthService $googleOAuthService,
        JWTTokenManagerInterface $jwtTokenManager
    ) {
        $this->tenantManager = $tenantManager;
        $this->errorResponseService = $errorResponseService;
        $this->requestValidatorService = $requestValidatorService;
        $this->googleOAuthService = $googleOAuthService;
        $this->jwtTokenManager = $jwtTokenManager;
    }

    #[Route('/auth/google', name: 'auth_google', methods: ['POST'])]
    public function googleLogin(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();
        $data = json_decode($request->getContent(), true);
        $idToken = $data['credential_google'] ?? null;

        if (!$idToken) {
            return $this->errorResponseService->createErrorResponse(
                GoogleAuthErrorCodes::TOKEN_NOT_PROVIDED
            );
        }

        $client = new Google_Client(['client_id' => $_ENV['GOOGLE_CLIENT_ID']]);
        $payload = $client->verifyIdToken($idToken);

        if (!$payload) {
            return $this->errorResponseService->createErrorResponse(
                GoogleAuthErrorCodes::INVALID_TOKEN
            );
        }

        $email = $payload['email'] ?? null;

        if (!$email) {
            return $this->errorResponseService->createErrorResponse(
                GoogleAuthErrorCodes::EMAIL_NOT_FOUND_IN_TOKEN
            );
        }

        $user = $em->getRepository(User::class)->findOneBy([
            'email' => $email,
            'status' => Status::ACTIVE,
        ]);

        // Mostrar info útil si no se encontró el usuario
        if (!$user) {
            dd('⚠️ Usuario no encontrado o inactivo', [
                'email' => $email,
                'payload' => $payload,
            ]);
        }

        $token = $this->jwtTokenManager->create($user);

        return new JsonResponse([
            'message' => 'Inicio de sesión exitoso',
            'token' => $token,
            'user' => [
                'id' => $user->getId(),
                'name' => $user->getName(),
                'email' => $user->getEmail(),
            ]
        ]);
    }
}
*/
