<?php
namespace App\Controller\Api;


use App\Entity\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use App\Entity\Company;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use App\DTO\Auth\RegistrationRequest;
use App\DTO\Auth\ChangePasswordRequest;
use App\DTO\Auth\LoginRequest;
use App\Enum\Status;
use App\Service\PhoneVerificationService;
use App\Service\ErrorResponseService;
use App\Service\RequestValidatorService;
use App\Service\TenantManager;
use App\Service\EntityProxyCleanerService;
use App\Service\AutoProxyCleanupService;
use App\Service\TwilioWhatsAppService;
use App\Service\ImagePathService;
use App\Traits\ProxyHandlerTrait;
use App\Enum\ErrorCodes\Api\AuthErrorCodes;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Psr\Log\LoggerInterface;

#[Route('/{dominio}/api')]




class AuthController extends AbstractController
{
    use ProxyHandlerTrait;
    private UserPasswordHasherInterface $passwordHasher;
    private PhoneVerificationService $phoneVerificationService;
    private ErrorResponseService $errorResponseService;
    private RequestValidatorService $requestValidatorService;
    private TenantManager $tenantManager;
    private JWTTokenManagerInterface $jwtTokenManager;
    private EntityProxyCleanerService $proxyCleanerService;
    private AutoProxyCleanupService $autoProxyCleanupService;
    private TwilioWhatsAppService $twilioWhatsAppService;
    private ImagePathService $imagePathService;
    private LoggerInterface $logger;

    public function __construct(
        UserPasswordHasherInterface $passwordHasher,
        PhoneVerificationService $phoneVerificationService,
        ErrorResponseService $errorResponseService,
        TenantManager $tenantManager,
        JWTTokenManagerInterface $jwtTokenManager,
        RequestValidatorService $requestValidatorService,
        EntityProxyCleanerService $proxyCleanerService,
        AutoProxyCleanupService $autoProxyCleanupService,
        TwilioWhatsAppService $twilioWhatsAppService,
        ImagePathService $imagePathService,
        LoggerInterface $logger
    ) {
        $this->passwordHasher = $passwordHasher;
        $this->phoneVerificationService = $phoneVerificationService;
        $this->errorResponseService = $errorResponseService;
        $this->tenantManager = $tenantManager;
        $this->jwtTokenManager = $jwtTokenManager;
        $this->requestValidatorService = $requestValidatorService;
        $this->proxyCleanerService = $proxyCleanerService;
        $this->autoProxyCleanupService = $autoProxyCleanupService;
        $this->twilioWhatsAppService = $twilioWhatsAppService;
        $this->imagePathService = $imagePathService;
        $this->logger = $logger;
    }

    #[Route('/register', name: 'api_register', methods: ['POST'])]
    public function register(string $dominio, Request $request, RequestValidatorService $requestValidator): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            $registrationRequest = $requestValidator->validateAndMap($request, RegistrationRequest::class);
            if ($registrationRequest instanceof JsonResponse) {
                return $registrationRequest;
            }

            // Buscar usuario activo y no verificado
            $userExists = $em->getRepository(User::class)->findOneBy([
                'curp' => $registrationRequest->curp,
                'status' => Status::ACTIVE,
                'verified' => Status::INACTIVE,
            ]);
            if (!$userExists) {
                return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_USER_NOT_FOUND_OR_ACTIVE,
                    [
                        'curp' => $registrationRequest->curp
                    ]
                );
            }

            // Verificar email solo en el tenant actual
            $emailExists = $em->getRepository(User::class)->findOneBy([
                'email' => $registrationRequest->email,
                'status' => Status::ACTIVE,
                'verified' => Status::ACTIVE,
            ]);

            if ($emailExists) {
                return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_EMAIL_ALREADY_REGISTERED,
                    [
                        'email' => $registrationRequest->email
                    ]
                );
            }

            // Limpiar el usuario de problemas de proxy ANTES de usarlo
            $cleanUser = $this->proxyCleanerService->cleanAndReloadUser($userExists, $em);

            // Actualizar datos del usuario
            $user = $this->updateUser($registrationRequest, $cleanUser);

            // Generar código de verificación ANTES de persistir
            $verificationCode = random_int(100000, 999999);
            $user->setVerificationCode((string)$verificationCode);

            // Persistir cambios primero
            $em->persist($user);
            $em->flush();

            // NO enviar SMS aquí - se enviará cuando React Native llame a /verify-phone-number
            $this->logger->info('Usuario registrado exitosamente - SMS se enviará en verificación de teléfono', [
                'user_id' => $user->getId(),
                'phone_number' => $user->getPhoneNumber(),
                'verification_code_generated' => $verificationCode
            ]);

            return new JsonResponse([
                'message' => 'Usuario registrado exitosamente. Use /verify-phone-number para recibir el código SMS.',
                'user_id' => $user->getId(),
                'code' => 201,
                'next_step' => 'call_verify_phone_number_endpoint'
            ], 201);

        } catch (\Exception $e) {
            // Log del error específico
            $this->logger->error('Error en registro de usuario', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'dominio' => $dominio
            ]);

            // Si es un error de proxy, limpiar EntityManager
            if (str_contains($e->getMessage(), 'Proxies\\__CG__') ||
                str_contains($e->getMessage(), 'entity identifier associated with the UnitOfWork')) {
                try {
                    $this->tenantManager->clearCurrentEntityManager();
                } catch (\Exception $clearException) {
                    // Ignorar errores al limpiar
                }
            }

            return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_REGISTRATION_FAILED,
                [
                    'message' => 'Error interno durante el registro. Por favor intente nuevamente.'
                ]
            );
        }
    }

    #[Route('/users/{userId}/password', name: 'api_change_password', methods: ['PATCH'])]
    public function changePassword(string $dominio, int $userId, Request $request, RequestValidatorService $requestValidator): JsonResponse
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $em = $this->tenantManager->getEntityManager();

            $changePasswordRequest = $requestValidator->validateAndMap($request, ChangePasswordRequest::class);
            if ($changePasswordRequest instanceof JsonResponse) {
                return $changePasswordRequest;
            }

            $user = $em->getRepository(User::class)->findOneBy([
                'id' => $userId,
                'status' => Status::ACTIVE,
            ]);
            if (!$user) {
                return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_USER_NOT_FOUND_OR_INACTIVE,
                    [
                        'user_id' => $userId
                    ]
                );
            }

            // Limpiar y recargar la entidad User para evitar problemas de proxy
            $user = $this->proxyCleanerService->cleanAndReloadUser($user, $em);

            if (!$this->passwordHasher->isPasswordValid($user, $changePasswordRequest->current_password)) {
                return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_USER_INCORRECT_PASSWORD,
                    [
                        'user_id' => $userId
                    ]
                );
            }

            $passwordHashed = $this->passwordHasher->hashPassword($user, $changePasswordRequest->new_password);
            $user->setPassword($passwordHashed);
            $user->setLastSeen(new \DateTimeImmutable());
            $user->setUpdatedAt(new \DateTimeImmutable());

            $em->persist($user);
            $em->flush();

            return new JsonResponse([
                'message' => 'Contraseña cambiada con éxito.',
                'code' => 200,
            ], 200);

        } catch (\Exception $e) {
            // Manejar errores de proxy específicamente
            $this->clearEntityManagerOnProxyError($e, $em);

            // Si es un error de proxy, devolver un error más específico
            if (str_contains($e->getMessage(), 'Proxies\\__CG__') ||
                str_contains($e->getMessage(), 'entity identifier associated with the UnitOfWork')) {

                return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_INTERNAL_ERROR,
                    [
                        'message' => 'Error interno del sistema. Por favor intente nuevamente.'
                    ]
                );
            }

            // Re-lanzar otros errores
            throw $e;
        }
    }

    #[Route('/login', name: 'api_login', methods: ['POST'])]
    public function login(string $dominio, Request $request, RequestValidatorService $requestValidator): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $loginRequest = $requestValidator->validateAndMap($request, LoginRequest::class);
        if ($loginRequest instanceof JsonResponse) {
            return $loginRequest;
        }

        $user = $em->createQuery(
            'SELECT u, c FROM App\\Entity\\User u LEFT JOIN u.company c WHERE u.email = :email AND u.status = :status AND u.verified = :verified'
        )
        ->setParameter('email', $loginRequest->email)
        ->setParameter('status', Status::ACTIVE)
        ->setParameter('verified', Status::ACTIVE)
        ->getOneOrNullResult();

        if (!$user) {
            return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_USER_NOT_FOUND_OR_INACTIVE,
                [
                    'email' => $loginRequest->email
                ]
            );
        }

        if (!$this->passwordHasher->isPasswordValid($user, $loginRequest->password)) {
            return $this->errorResponseService->createErrorResponse(AuthErrorCodes::AUTH_USER_INCORRECT_PASSWORD,
                [
                    'email' => $loginRequest->email
                ]
            );
        }

        // Usar el servicio automático para manejar el usuario de forma segura
        return $this->autoProxyCleanupService->safeUserOperation($user, $em, function($cleanUser) use ($em) {
            // Update last seen
            $cleanUser->setLastSeen(new \DateTimeImmutable());

            // Persistir y hacer flush de forma segura
            $this->autoProxyCleanupService->safePersistAndFlush($em, $cleanUser);

            // Generate JWT token
            $token = $this->jwtTokenManager->create($cleanUser);

            // Obtener company_id de forma segura
            $companyId = $this->autoProxyCleanupService->safeGetUserCompanyId($cleanUser, $em);

            return new JsonResponse([
                'token' => $token,
                'user_id' => $cleanUser->getId(),
                'company_id' => $companyId,
                'user' => [
                    'user_id' => $cleanUser->getId(),
                    'email' => $cleanUser->getEmail(),
                    'name' => $cleanUser->getName(),
                    'last_name' => $cleanUser->getLastName(),
                    'photo' => $this->imagePathService->generateFullPath($cleanUser->getPhoto()),
                    'phone_number' => $cleanUser->getPhoneNumber(),
                    'company_id' => $companyId,
                ],
            ]);
        });
    }

    private function updateUser(RegistrationRequest $registrationRequest, User $user): User
    {
        /*$user->setCurp($registrationRequest->curp)*/;
        $user->setEmail($registrationRequest->email);
        $user->setPhoneNumber($registrationRequest->phone_number);
        /*$user->setStatus(Status::ACTIVE);*/
        $user->setUpdatedAt(new \DateTimeImmutable());
        $user->setLastSeen(new \DateTimeImmutable());
        /*$user->setEmployeeNumber($registrationRequest->employee_number);*/

        $passwordHashed = $this->passwordHasher->hashPassword($user, $registrationRequest->password);
        $user->setPassword($passwordHashed);

        return $user;
    }




}