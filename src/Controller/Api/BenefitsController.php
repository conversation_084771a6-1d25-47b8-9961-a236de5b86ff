<?php
namespace App\Controller\Api;

use App\DTO\BenefitsGetRequest;
use App\Entity\Benefit;
use App\Entity\Company;
use App\Enum\ErrorCodes\Api\BenefitsErrorCodes;
use App\Enum\Status;
use App\Repository\BenefitRepository;
use App\Service\ErrorResponseService;
use App\Service\ImagePathService;
use App\Service\RequestValidatorService;
use App\Service\TenantManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/{dominio}/api')]
class BenefitsController extends AbstractController
{
    private TenantManager $tenantManager;
    private ErrorResponseService $errorResponseService;
    private RequestValidatorService $requestValidatorService;

    private ImagePathService $imagePathService;

    public function __construct(
        TenantManager $tenantManager,
        ErrorResponseService $errorResponseService,
        RequestValidatorService $requestValidatorService,
        ImagePathService $imagePathService
    ) {
        $this->tenantManager = $tenantManager;
        $this->errorResponseService = $errorResponseService;
        $this->requestValidatorService = $requestValidatorService;
        $this->imagePathService = $imagePathService;
    }

    #[Route('/benefits', name: 'api_benefits', methods: ['GET'])]
    public function getBenefits(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $benefitsGetRequest = $this->requestValidatorService->validateAndMap($request, BenefitsGetRequest::class, true);
        if ($benefitsGetRequest instanceof JsonResponse) {
            return $benefitsGetRequest;
        }

        try {
            $company = $em->getRepository(Company::class)->findOneBy([
                'id' => $benefitsGetRequest->company_id,
                'status' => Status::ACTIVE,
            ]);
            if (!$company) {
                return $this->errorResponseService->createErrorResponse( BenefitsErrorCodes::BENEFITS_COMPANY_NOT_FOUND_OR_INACTIVE, 
                    [
                        'company_id' => $benefitsGetRequest->company_id,
                    ]
                );
            }

            $page = $benefitsGetRequest->page ?? 1;
            $perPage = $benefitsGetRequest->per_page;

            /** @var BenefitRepository $benefitRepo */
            $benefitRepo = $em->getRepository(Benefit::class);
            $paginator = $benefitRepo->findActiveBenefitsByCompany($company, $page, $perPage);
            $benefits = iterator_to_array($paginator->getIterator());

            if (empty($benefits)) {
                return $this->errorResponseService->createErrorResponse(BenefitsErrorCodes::BENEFITS_ACTIVE_NOT_FOUND,
                    [
                        'company_id' => $benefitsGetRequest->company_id,
                        'page' => $page,
                        'per_page' => $perPage,
                    ]
                );
            }

            $response = array_map(fn($benefit) => $this->mapBenefitToArray($benefit), $benefits);
            $totalItems = count($paginator);
            $totalPages = $perPage ? (int) ceil($totalItems / $perPage) : 1;

            return new JsonResponse([
                'benefits' => $response,
                'code' => 200,
                'pagination' => [
                    'page' => $page,
                    'per_page' => $perPage ?? $totalItems,
                    'total_items' => $totalItems,
                    'total_pages' => $totalPages,
                    'has_next_page' => $perPage ? $page < $totalPages : false,
                    'has_previous_page' => $page > 1,
                ],
            ], 200);
        } catch (\Exception $e) {
            return $this->errorResponseService->createErrorResponse(
                BenefitsErrorCodes::BENEFITS_INTERNAL_ERROR,
                [
                    'message' => $e->getMessage(),
                ],
                500
            );
        }
    }


    #[Route('/benefits/{id}', name: 'api_benefits_show', methods: ['GET'])]
    public function show(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $benefit = $em->getRepository(Benefit::class)->find($id);

        if (!$benefit) {
            return $this->json([
                'status' => 'error',
                'message' => 'Beneficio no encontrado'
            ], 404);
        }

        return $this->json([
            'status' => 'success',
            'data' => $benefit
        ]);
    }

    #[Route('/benefits', name: 'api_benefits_create', methods: ['POST'])]
    public function create(string $dominio, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $data = json_decode($request->getContent(), true);

        $benefit = new Benefit();
        // Aquí iría la lógica para establecer las propiedades del beneficio
        // según los datos recibidos en $data

        $em->persist($benefit);
        $em->flush();

        return $this->json([
            'status' => 'success',
            'message' => 'Beneficio creado exitosamente',
            'data' => $benefit
        ], 201);
    }

    #[Route('/benefits/{id}', name: 'api_benefits_update', methods: ['PUT'])]
    public function update(string $dominio, int $id, Request $request): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $benefit = $em->getRepository(Benefit::class)->find($id);

        if (!$benefit) {
            return $this->json([
                'status' => 'error',
                'message' => 'Beneficio no encontrado'
            ], 404);
        }

        $data = json_decode($request->getContent(), true);
        // Aquí iría la lógica para actualizar las propiedades del beneficio
        // según los datos recibidos en $data

        $em->flush();

        return $this->json([
            'status' => 'success',
            'message' => 'Beneficio actualizado exitosamente',
            'data' => $benefit
        ]);
    }

    #[Route('/benefits/{id}', name: 'api_benefits_delete', methods: ['DELETE'])]
    public function delete(string $dominio, int $id): JsonResponse
    {
        $this->tenantManager->setCurrentTenant($dominio);
        $em = $this->tenantManager->getEntityManager();

        $benefit = $em->getRepository(Benefit::class)->find($id);

        if (!$benefit) {
            return $this->json([
                'status' => 'error',
                'message' => 'Beneficio no encontrado'
            ], 404);
        }

        $em->remove($benefit);
        $em->flush();

        return $this->json([
            'status' => 'success',
            'message' => 'Beneficio eliminado exitosamente'
        ]);
    }

    private function filterBenefits(Collection $benefits): array
    {
        $filteredBenefits = [];

        foreach ($benefits as $benefit) {

            if ($benefit->getStatus() === Status::ACTIVE) {
                $includeBenefit = true;

                $benefitStartDate = $benefit->getValidityStartDate();
                $benefitEndDate = $benefit->getValidityEndDate();

                $currentDate = new \DateTime();

                if ($currentDate < $benefitStartDate || $currentDate > $benefitEndDate) {
                    $includeBenefit = false;
                }

                if ($includeBenefit) {
                    $filteredBenefits[] = $benefit;
                }
            }
        }
        usort($filteredBenefits, function ($a, $b) {
            return $a->getValidityStartDate() <=> $b->getValidityStartDate();
        });

        return $filteredBenefits;
    }

    private function mapBenefitToArray($benefit): array
    {
        return [
            'id' => $benefit->getId(),
            'title' => $benefit->getTitle(),
            'description' => $benefit->getDescription(),
            'validity_start_date' => $benefit->getValidityStartDate()->format('Y-m-d H:i:s'),
            'validity_end_date' => $benefit->getValidityEndDate()->format('Y-m-d H:i:s'),
            'image' => $this->imagePathService->generateFullPath($benefit->getImage()),
        ];
    }
}