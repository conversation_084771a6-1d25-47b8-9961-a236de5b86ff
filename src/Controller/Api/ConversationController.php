<?php
namespace App\Controller\Api;

use App\DTO\MessageDTO;
use App\DTO\MessageRequest;
use App\Entity\Company;
use App\Entity\Conversation;
use App\Entity\UnreadMessage;
use App\Entity\User;
use App\Enum\ErrorCodes\Api\UserErrorCodes;
use App\Enum\Status;
use App\Factory\MessageFactory;
use App\Repository\ConversationRepository;
use App\Service\Auth\MercureJwtGeneratorService;
use App\Service\ErrorResponseService;
use App\Service\TenantManager;
use App\Service\TopicService;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Kreait\Firebase\Messaging\Messages;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Mercure\HubInterface;
use Symfony\Component\Mercure\Update;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/{dominio}/api')]
class ConversationController extends AbstractController
{

    private string $mercurePublicUrl;


    public function __construct(
        private readonly TenantManager $tenantManager,
        private readonly TopicService $topicService,
        private readonly JWTTokenManagerInterface $JWTTokenManager,
        private readonly ErrorResponseService $errorResponseService, // Para manejar errores
        private readonly EntityManagerInterface $em,
        string $mercurePublicUrl,
        private readonly MercureJwtGeneratorService $mercureJwtGenerator,
        private readonly ConversationRepository $conversationRepository,
        private readonly MessageFactory $messageFactory,
        private readonly HubInterface $hub,
    )
    {
        $this->mercurePublicUrl = $mercurePublicUrl;
    }

    #[Route('/users/{sender}/subscribe', name: 'app_conversation_load', methods: ['GET'])]
    public function subscribe(string $dominio, User $sender): JsonResponse
    {
        // Verificar si el usuario existe, si no, devolver error
        if (!$sender) {
            return $this->errorResponseService->createErrorResponse(UserErrorCodes::USER_NOT_FOUND);
        }

        // Configurar el tenant
        $this->tenantManager->setCurrentTenant($dominio);


        // Verificar si el usuario tiene una conversación
        $conversations = $sender->getConversations();

        $company = $sender->getCompany();

        if ($company) {
            // Asegurarse de que la entidad Company esté completamente cargada
            $company = $this->em->getRepository(Company::class)->find($company->getId());
        }


        if ($conversations->isEmpty()) {
            $conversation = new Conversation();
            $conversation->setStatus('1');
            $conversation->setCompany($company);
            $conversation->setCreatedAt(new \DateTime());

            // Aquí agregas el usuario a la conversación:
            $conversation->addUser($sender);

            $this->em->persist($conversation);
            $this->em->flush();

            $topic = $this->topicService->getTopicUrl($conversation);

            $jwt = $this->mercureJwtGenerator->generate([$topic]);

            $sseUrl = $this->mercurePublicUrl . '?topic=' . urlencode($topic);

            return new JsonResponse([
                'conversation' => $conversation->getId(),
                'messages' => [],
                'topic' => $sseUrl,
                'authorization' => $jwt,
            ], 200);
        }


        $conversation = $conversations->first();

        // Si ya existe una conversación, obtenemos los mensajes
        $messages = $this->conversationRepository->getMessagesByConversationId($conversation);

        $topic = $this->topicService->getTopicUrl($conversation);

        $jwt = $this->mercureJwtGenerator->generate([$topic]);

        $sseUrl = $this->mercurePublicUrl . '?topic=' . urlencode($topic);

        return new JsonResponse([
            'conversation' => $conversation->getId(),
            'messages' => $messages,
            'topic' => $sseUrl,
            'authorization' => $jwt,
        ]);
    }

    #[Route('/users/{sender}/publish', name: 'app_conversation_publish', methods: ['POST'])]
    public function publish(#[MapRequestPayload] MessageRequest $payload, User $sender): JsonResponse
    {
        $conversation = $this->conversationRepository->find($payload->conversationId);

        $message = $this->messageFactory->create(
            conversation: $conversation,
            author: $sender,
            content: $payload->content
        );

        $unreadMessage = new UnreadMessage();

        $unreadMessage->setConversation($conversation);
        $unreadMessage->setMessage($message);

        $this->em->persist($unreadMessage);
        $this->em->flush();

        $data = [
            'author' => [
                'id' => $sender->getId(),
                'name' => $sender->getName(),
                'lastName' => $sender->getLastname(),
                'role' => $sender->getRoles(),
            ],
            'content' => $message->getContent(),
            'id' => $message->getId(),
            'createdAt' => $message->getCreatedAt()->setTimezone(new \DateTimeZone('America/Mexico_City'))->format('d/m/Y H:i'),
        ];

        $update = new Update(
            topics: $this->topicService->getTopicUrl($conversation),
            data: json_encode($data),
            private: true
        );

        $this->hub->publish($update);

        return new JsonResponse([
            'conversation' => $conversation->getId(),

        ], 200);
    }

    // Método para generar el JWT
    private function generateJwt(User $sender, Conversation $conversation): string
    {
        $topic = $this->topicService->getTopicUrl($conversation);

        // Generar el JWT con el topic de la conversación
        return $this->JWTTokenManager->createFromPayload($sender, [
            'mercure' => [
                'subscribe' => [$topic],  // Solo suscripción para el topic específico
            ]
        ]);
    }

    private function messagesToArray(Collection $messages): array
    {
        return array_map(
            fn ($message) => new MessageDTO($message),
            $messages->toArray()
        );
    }



}