<?php

namespace App\Controller;

use App\DTO\MessageRequest;
use App\Entity\User;
use App\Factory\MessageFactory;
use App\Repository\ConversationRepository;
use App\Repository\MessageRepository;
use App\Service\TopicService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Mercure\HubInterface;
use Symfony\Component\Mercure\Update;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @method User|null getUser()
 */

#[Route('/{dominio}/message')]
final class MessageController extends AbstractController
{

    public function __construct(
        private ConversationRepository $conversationRepository,
        private readonly MessageFactory $messageFactory,
        private readonly TopicService $topicService,
        private readonly HubInterface $hub,
    )
    {
    }

    #[Route('/', name: 'app_message_create', methods: ['POST'])]
    public function create(#[MapRequestPayload] MessageRequest $payload): Response
    {
        try {
            $conversation = $this->conversationRepository->find($payload->conversationId);

            $message = $this->messageFactory->create(
                conversation: $conversation,
                author: $this->getUser(),
                content: $payload->content
            );

            $data = [
                'author' => [
                    'id' => $this->getUser()->getId(),
                    'name' => $this->getUser()->getName(),
                    'lastName' => $this->getUser()->getLastname(),
                    'role' => $this->getUser()->getRoles(),
                ],
                'content' => $message->getContent(),
                'id' => $message->getId(),
                'createdAt' => $message->getCreatedAt()->setTimezone(new \DateTimeZone('America/Mexico_City'))->format('d/m/Y H:i'),
            ];

            $update = new Update(
                topics: $this->topicService->getTopicUrl($conversation),
                data: json_encode($data),
                private: true
            );

            $this->hub->publish($update);

            return new Response($message->getId(), Response::HTTP_CREATED);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }
}
