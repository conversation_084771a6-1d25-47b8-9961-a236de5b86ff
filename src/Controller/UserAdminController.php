<?php

namespace App\Controller;

use App\Entity\User;
use App\Entity\Role;
use App\Entity\Company;
use App\Form\UserAdminType;
use App\Form\UserAdminEditType;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use App\Service\ImageUploadService;
use App\Service\RegionAccessService;
use App\Service\TenantManager;
use App\Enum\Status;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[Route('/{dominio}/admin/user')]
final class UserAdminController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('', name: 'app_user_admin_index', methods: ['GET'])]
    public function index(string $dominio, UserRepository $userRepository, RegionAccessService $regionAccessService): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $queryBuilder = $userRepository->createQueryBuilder('u')
                ->join('u.role', 'r')
                ->where('r.name IN (:roles)')
                ->andWhere('u.status = :status')
                ->setParameter('roles', ['ROLE_ADMIN', 'ROLE_LIDER'])
                ->setParameter('status', Status::ACTIVE);

            // Get the current user
            $currentUser = $this->getUser();
            $currentUserRoles = $currentUser->getRoles();

            // If current user is ROLE_LIDER, filter users based on regions
            if (in_array('ROLE_LIDER', $currentUserRoles) && !in_array('ROLE_ADMIN', $currentUserRoles)) {
                // For ROLE_LIDER, only show users from the same regions
                $regionAccessService->applyRegionFilter($queryBuilder, 'u', 'u.regions');
            }

            $users = $queryBuilder->getQuery()->getResult();

            return $this->render('user/admin/index.html.twig', [
                'users' => $users,
                'dominio' => $dominio
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/new', name: 'app_user_admin_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager, UserPasswordHasherInterface $passwordHasher, ImageUploadService $imageUploadService): Response
    {
        $this->tenantManager->setCurrentTenant($dominio);

        $user = new User();
        $user->setStatus(Status::ACTIVE);
        $user->setCreatedAt(new \DateTimeImmutable());
        $user->setUpdatedAt(new \DateTimeImmutable());
        $form = $this->createForm(UserAdminType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Manejar el password si se proporcionó
            $plainPassword = $form->get('password')->getData();
            if ($plainPassword) {
                $hashedPassword = $passwordHasher->hashPassword($user, $plainPassword);
                $user->setPassword($hashedPassword);
            }

            // Manejar la foto de perfil si se subió
            $photoFile = $form->get('photo')->getData();
            if ($photoFile) {
                try {
                    $photoPath = $imageUploadService->uploadImage($photoFile, 'profile');
                    $user->setPhoto($photoPath);
                } catch (\Exception $e) {
                    $this->addFlash('error', 'Error al subir la imagen: ' . $e->getMessage());
                }
            }

            $entityManager->persist($user);
            $entityManager->flush();

            $this->addFlash('success', 'Usuario creado correctamente.');
            return $this->redirectToRoute('app_user_admin_index', ['dominio' => $dominio]);
        }

        return $this->render('user/admin/new_admin.html.twig', [
            'user' => $user,
            'form' => $form,
            'dominio' => $dominio
        ]);
    }

    #[Route('/{id}', name: 'app_user_admin_show', methods: ['GET'])]
    public function show(string $dominio, User $user): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('user/admin/show.html.twig', [
                'user' => $user,
                'dominio' => $dominio
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_user_admin_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager, UserPasswordHasherInterface $passwordHasher, ImageUploadService $imageUploadService): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            
            $form = $this->createForm(UserAdminEditType::class, $user);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Manejar el password si se proporcionó
                $plainPassword = $form->get('password')->getData();
                if ($plainPassword) {
                    $hashedPassword = $passwordHasher->hashPassword($user, $plainPassword);
                    $user->setPassword($hashedPassword);
                }

                // Manejar la foto de perfil si se subió
                $photoFile = $form->get('photo')->getData();
                if ($photoFile) {
                    try {
                        $photoPath = $imageUploadService->uploadImage($photoFile, 'profile');
                        $user->setPhoto($photoPath);
                    } catch (\Exception $e) {
                        $this->addFlash('error', 'Error al subir la imagen: ' . $e->getMessage());
                    }
                }

                $entityManager->flush();

                $this->addFlash('success', 'Usuario actualizado correctamente.');
                return $this->redirectToRoute('app_user_admin_index', ['dominio' => $dominio]);
            }

            return $this->render('user/admin/edit.html.twig', [
                'user' => $user,
                'form' => $form,
                'dominio' => $dominio
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_user_admin_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            
            if ($this->isCsrfTokenValid('delete'.$user->getId(), $request->request->get('_token'))) {
                $user->setStatus(Status::INACTIVE);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_user_admin_index', ['dominio' => $dominio]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit-complete', name: 'app_user_admin_edit_complete', methods: ['GET', 'POST'])]
    public function editComplete(string $dominio, Request $request, User $user, EntityManagerInterface $entityManager, UserPasswordHasherInterface $passwordHasher, ImageUploadService $imageUploadService): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);

            $form = $this->createForm(UserAdminEditType::class, $user);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Manejar el password si se proporcionó
                $plainPassword = $form->get('password')->getData();
                if ($plainPassword) {
                    $hashedPassword = $passwordHasher->hashPassword($user, $plainPassword);
                    $user->setPassword($hashedPassword);
                }

                // Manejar la foto de perfil si se subió
                $photoFile = $form->get('photo')->getData();
                if ($photoFile) {
                    try {
                        $photoPath = $imageUploadService->uploadImage($photoFile, 'profile');
                        $user->setPhoto($photoPath);
                    } catch (\Exception $e) {
                        $this->addFlash('error', 'Error al subir la imagen: ' . $e->getMessage());
                    }
                }

                $entityManager->flush();

                $this->addFlash('success', 'Usuario actualizado correctamente.');
                return $this->redirectToRoute('app_user_admin_edit_complete', ['dominio' => $dominio, 'id' => $user->getId()]);
            }

            // Obtener formularios contestados por el usuario
            $formEntries = $entityManager->getRepository(\App\Entity\FormEntry::class)
                ->findBy(['user' => $user], ['created_at' => 'DESC']);

            return $this->render('user/admin/edit_complete.html.twig', [
                'user' => $user,
                'form' => $form,
                'dominio' => $dominio,
                'form_entries' => $formEntries
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
}
