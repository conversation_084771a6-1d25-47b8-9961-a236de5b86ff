<?php

namespace App\Controller;

use App\Entity\Benefit;
use App\Form\BenefitType;
use App\Repository\BenefitRepository;
use App\Service\ImageUploadService;
use App\Service\ImagePathService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Enum\Status;

#[Route('/{dominio}/benefit')]
final class BenefitController extends AbstractController
{
    private ImageUploadService $imageUploadService;
    private ImagePathService $imagePathService;
    private TenantManager $tenantManager;

    public function __construct(ImageUploadService $imageUploadService, ImagePathService $imagePathService, TenantManager $tenantManager)
    {
        $this->imageUploadService = $imageUploadService;
        $this->imagePathService = $imagePathService;
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_benefit_index', methods: ['GET'])]
    public function index(string $dominio, Request $request, BenefitRepository $benefitRepository, EntityManagerInterface $entityManager): Response
    {   
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $benefit = new Benefit(); 
            $form = $this->createForm(BenefitType::class, $benefit);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'benefit');
                    $benefit->setImage($relativePath);
                }

                $entityManager->persist($benefit);
                $entityManager->flush();

                return $this->redirectToRoute('app_benefit_index', ['dominio' => $dominio]);
            }

            $user = $this->getUser();
            $userRoles = $user->getRoles();

            // If user is ROLE_ADMIN or ROLE_LIDER, filter by their regions
            if (in_array('ROLE_ADMIN', $userRoles) || in_array('ROLE_LIDER', $userRoles)) {
                $userRegions = $user->getRegions();

                // Get all companies from user's regions
                $regionCompanies = [];
                foreach ($userRegions as $region) {
                    foreach ($region->getCompanies() as $company) {
                        $regionCompanies[] = $company;
                    }
                }

                // Get benefits for these companies
                $benefits = $benefitRepository->findActiveByCompanies($regionCompanies);
            } else {
                // For other roles, show all active benefits
                $benefits = $benefitRepository->findBy(['status' => Status::ACTIVE]);
            }

            return $this->render('benefit/index.html.twig', [
                'benefits' => $benefits,
                'form' => $form->createView(),
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/new', name: 'app_benefit_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $benefit = new Benefit();
            $form = $this->createForm(BenefitType::class, $benefit);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'benefit');
                    $benefit->setImage($relativePath);
                }

                $benefit->setStatus(Status::ACTIVE);
                $benefit->setCreatedAt(new \DateTimeImmutable());
                $benefit->setUpdatedAt(new \DateTimeImmutable());

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $benefit->addCompany($company);
                        }
                    }
                }

                $entityManager->persist($benefit);
                $entityManager->flush();

                return $this->redirectToRoute('app_benefit_index', ['dominio' => $dominio]);
            }

            return $this->render('benefit/new.html.twig', [
                'benefit' => $benefit,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_benefit_show', methods: ['GET'])]
    public function show(string $dominio, Benefit $benefit): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('benefit/show.html.twig', [
                'benefit' => $benefit,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_benefit_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Benefit $benefit, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(BenefitType::class, $benefit);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'benefit');
                    $benefit->setImage($relativePath);
                }

                $benefit->setUpdatedAt(new \DateTimeImmutable());

                // Clear existing companies
                foreach ($benefit->getCompanies() as $company) {
                    $benefit->removeCompany($company);
                }

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $benefit->addCompany($company);
                        }
                    }
                }

                $entityManager->flush();

                return $this->redirectToRoute('app_benefit_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('benefit/edit.html.twig', [
                'benefit' => $benefit,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_benefit_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Benefit $benefit, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$benefit->getId(), $request->request->get('_token'))) {
                $benefit->setStatus(Status::INACTIVE);
                $benefit->setUpdatedAt(new \DateTimeImmutable());

                $entityManager->persist($benefit);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_benefit_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
}
