<?php

namespace App\Controller;

use App\Entity\Event;
use App\Form\EventType;
use App\Repository\EventRepository;
use App\Service\ImageUploadService;
use App\Service\ImagePathService;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;
use App\Enum\Status;

#[Route('/{dominio}/event')]
final class EventController extends AbstractController
{
    private ImageUploadService $imageUploadService;
    private ImagePathService $imagePathService;
    private TenantManager $tenantManager;

    public function __construct(ImageUploadService $imageUploadService, ImagePathService $imagePathService, TenantManager $tenantManager)
    {
        $this->imageUploadService = $imageUploadService;
        $this->imagePathService = $imagePathService;
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_event_index', methods: ['GET'])]
    public function index(string $dominio, EventRepository $eventRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $user = $this->getUser();
            $userRoles = $user->getRoles();

            // If user is ROLE_ADMIN or ROLE_LIDER, filter by their regions
            if (in_array('ROLE_ADMIN', $userRoles) || in_array('ROLE_LIDER', $userRoles)) {
                $userRegions = $user->getRegions();

                // Get all companies from user's regions
                $regionCompanies = [];
                foreach ($userRegions as $region) {
                    foreach ($region->getCompanies() as $company) {
                        $regionCompanies[] = $company;
                    }
                }

                // Get events for these companies
                $events = $eventRepository->findActiveByCompanies($regionCompanies);
            } else {
                // For other roles, show all active events
                $events = $eventRepository->findBy(['status' => Status::ACTIVE]);
            }

            return $this->render('event/index.html.twig', [
                'events' => $events,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/new', name: 'app_event_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $event = new Event();
            $form = $this->createForm(EventType::class, $event);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'event');
                    $event->setImage($relativePath);
                }

                $event->setStatus(Status::ACTIVE);
                $event->setCreatedAt(new \DateTimeImmutable());
                $event->setUpdatedAt(new \DateTimeImmutable());

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $event->addCompany($company);
                        }
                    }
                }

                $entityManager->persist($event);
                $entityManager->flush();

                return $this->redirectToRoute('app_event_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('event/new.html.twig', [
                'event' => $event,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_event_show', methods: ['GET'])]
    public function show(string $dominio, Event $event): Response
    {        
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('event/show.html.twig', [
                'event' => $event,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}/edit', name: 'app_event_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Event $event, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(EventType::class, $event);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {

                /** @var UploadedFile $imageFile */
                $imageFile = $form->get('image')->getData();

                if ($imageFile) {
                    $relativePath = $this->imageUploadService->uploadImage($imageFile, 'event');
                    $event->setImage($relativePath);
                }

                // Clear existing companies
                foreach ($event->getCompanies() as $company) {
                    $event->removeCompany($company);
                }

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $event->addCompany($company);
                        }
                    }
                }

                $entityManager->flush();

                return $this->redirectToRoute('app_event_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('event/edit.html.twig', [
                'event' => $event,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }

    #[Route('/{id}', name: 'app_event_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Event $event, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$event->getId(), $request->request->get('_token'))) {
                $event->setStatus(Status::INACTIVE);
                $event->setUpdatedAt(new \DateTimeImmutable());

                $entityManager->persist($event);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_event_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant error: ' . $e->getMessage(), $e);
        }
    }
}
