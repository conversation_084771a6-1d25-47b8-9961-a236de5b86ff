<?php

namespace App\Controller;

use App\Entity\Company;
use App\Enum\Status;
use App\Form\CompanyType;
use App\Repository\CompanyRepository;
use App\Service\TenantManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/{dominio}/company')]
final class CompanyController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_company_index', methods: ['GET'])]
    public function index(string $dominio, CompanyRepository $companyRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $user = $this->getUser();
            $userRoles = $user->getRoles();

            // If user is ROLE_ADMIN or ROLE_LIDER, filter by their regions
            if (in_array('ROLE_ADMIN', $userRoles) || in_array('ROLE_LIDER', $userRoles)) {
                $userRegions = $user->getRegions();

                // Get region IDs
                $regionIds = [];
                foreach ($userRegions as $region) {
                    $regionIds[] = $region->getId();
                }

                // Get companies for these regions
                $companies = $companyRepository->findActiveByRegions($regionIds);
            } else {
                // For other roles, show all active companies
                $companies = $companyRepository->findBy(['status' => Status::ACTIVE]);
            }

            return $this->render('company/index.html.twig', [
                'companies' => $companies,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/new', name: 'app_company_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $company = new Company();
            $form = $this->createForm(CompanyType::class, $company);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                $company->setStatus(Status::ACTIVE);
                $company->setCreatedAt(new \DateTimeImmutable());
                $company->setUpdatedAt(new \DateTimeImmutable());

                $entityManager->persist($company);
                $entityManager->flush();

                return $this->redirectToRoute('app_company_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('company/new.html.twig', [
                'company' => $company,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_company_show', methods: ['GET'])]
    public function show(string $dominio, Company $company): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('company/show.html.twig', [
                'company' => $company,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}/edit', name: 'app_company_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Company $company, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(CompanyType::class, $company);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                $company->setUpdatedAt(new \DateTimeImmutable());
                $entityManager->flush();

                return $this->redirectToRoute('app_company_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('company/edit.html.twig', [
                'company' => $company,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_company_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Company $company, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$company->getId(), $request->request->get('_token'))) {
                $company->setStatus(Status::INACTIVE);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_company_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }
}
