<?php

namespace App\Controller;

use App\Entity\Notification;
use App\Enum\Status;
use App\Enum\ErrorCodes\NotificationErrorCodes;
use App\Form\NotificationType;
use App\Repository\NotificationRepository;
use App\Service\ExpoNotificationService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Service\ApplicationErrorService;
use App\Service\PushNotificationService;
use App\Service\TenantManager;

#[Route('/{dominio}/notification')]
final class NotificationController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('/', name: 'app_notification_index', methods: ['GET'])]
    public function index(string $dominio, NotificationRepository $notificationRepository): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $user = $this->getUser();

            if (!$user) {
                $this->addFlash('error', 'Usuario no autenticado');
                return $this->redirectToRoute('app_login'); // O a donde consideres adecuado
            }

            $userRoles = $user->getRoles();

            // If user is ROLE_ADMIN or ROLE_LIDER, filter by their regions
            if (in_array('ROLE_ADMIN', $userRoles) || in_array('ROLE_LIDER', $userRoles)) {
                $userRegions = $user->getRegions();

                // Get all companies from user's regions
                $regionCompanies = [];
                foreach ($userRegions as $region) {
                    foreach ($region->getCompanies() as $company) {
                        $regionCompanies[] = $company;
                    }
                }

                // Get notifications for these companies
                $notifications = $notificationRepository->findActiveByCompanies($regionCompanies);
            } else {
                // For other roles, show all active notifications
                $notifications = $notificationRepository->findBy(['status' => Status::ACTIVE]);
            }

            return $this->render('notification/index.html.twig', [
                'notifications' => $notifications,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/new', name: 'app_notification_new', methods: ['GET', 'POST'])]
    public function new(string $dominio, Request $request, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $notification = new Notification();
            $form = $this->createForm(NotificationType::class, $notification);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                $notification->setStatus(Status::ACTIVE);
                $notification->setCreatedAt(new \DateTimeImmutable());
                $notification->setUpdatedAt(new \DateTimeImmutable());

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $notification->addCompany($company);
                        }
                    }
                }

                $entityManager->persist($notification);
                $entityManager->flush();

                return $this->redirectToRoute('app_notification_send', ['dominio' => $dominio, 'id' => $notification->getId()], Response::HTTP_SEE_OTHER);
            }

            return $this->render('notification/new.html.twig', [
                'notification' => $notification,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_notification_show', methods: ['GET'])]
    public function show(string $dominio, Notification $notification): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            return $this->render('notification/show.html.twig', [
                'notification' => $notification,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}/edit', name: 'app_notification_edit', methods: ['GET', 'POST'])]
    public function edit(string $dominio, Request $request, Notification $notification, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            $form = $this->createForm(NotificationType::class, $notification);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                // Clear existing companies
                foreach ($notification->getCompanies() as $company) {
                    $notification->removeCompany($company);
                }

                // Handle selected companies
                $selectedCompaniesStr = $request->request->get('selected_companies');
                if ($selectedCompaniesStr) {
                    $selectedCompanyIds = explode(',', $selectedCompaniesStr);
                    foreach ($selectedCompanyIds as $companyId) {
                        $company = $entityManager->getRepository(\App\Entity\Company::class)->find($companyId);
                        if ($company) {
                            $notification->addCompany($company);
                        }
                    }
                }

                $notification->setUpdatedAt(new \DateTimeImmutable());
                $entityManager->flush();

                return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
            }

            return $this->render('notification/edit.html.twig', [
                'notification' => $notification,
                'form' => $form,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}', name: 'app_notification_delete', methods: ['POST'])]
    public function delete(string $dominio, Request $request, Notification $notification, EntityManagerInterface $entityManager): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($this->isCsrfTokenValid('delete'.$notification->getId(), $request->request->get('_token'))) {
                $notification->setStatus(Status::INACTIVE);
                $entityManager->flush();
            }

            return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio], Response::HTTP_SEE_OTHER);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    #[Route('/{id}/send', name: 'app_notification_send', methods: ['GET', 'POST'])]
    public function expoNotification(
        string $dominio,
        Request $request,
        Notification $notification,
        ApplicationErrorService $applicationErrorService,
        ExpoNotificationService $expoNotificationService
    ): Response
    {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            if ($notification->getStatus() !== Status::ACTIVE) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NOT_ACTIVE['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NOT_ACTIVE, [
                    'notification_id' => $notification->getId(),
                ]);
                return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio]);
            }

            $companies = $notification->getCompanies();

            if ($companies->isEmpty()) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_COMPANY['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_COMPANY, [
                    'notification_id' => $notification->getId(),
                ]);
                return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio]);
            }

            $users = [];
            foreach ($companies as $company) {
                $users = array_merge($users, $company->getUsers()->toArray());
            }

            if (empty($users)) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_USERS['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_USERS, [
                    'notification_id' => $notification->getId(),
                ]);
                return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio]);
            }

            $deviceTokens = [];
            foreach ($users as $user) {
                foreach ($user->getDeviceTokens() as $token) {
                    $deviceTokens[] = $token->getToken();
                }
            }

            if (empty($deviceTokens)) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_USERS_TOKENS['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_USERS_TOKENS, [
                    'notification_id' => $notification->getId(),
                ]);
                return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio]);
            }

            $result = $expoNotificationService->sendExpoNotification(
                $deviceTokens,
                $notification->getTitle(),
                $notification->getMessage()
            );

            if ($result['success']) {
                $this->addFlash('success', 'Notificaciones enviadas correctamente');
            } else {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_SEND_FAILED['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_SEND_FAILED, [
                    'notification_id' => $notification->getId(),
                ]);
            }

            return $this->redirectToRoute('app_notification_index', ['dominio' => $dominio]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found');
        }
    }

    /*    #[Route('/{id}/send', name: 'app_notification_send', methods: ['POST'])]
        public function send(
            Notification $notification,
            PushNotificationService $pushNotificationService,
            ApplicationErrorService $applicationErrorService,
        ): Response {
            if($notification->getStatus() !== Status::ACTIVE) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NOT_ACTIVE['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NOT_ACTIVE, [
                    'notification_id' => $notification->getId(),
                ]);

                return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
            }

            $companies = $notification->getCompanies();
            if($companies->isEmpty()) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_COMPANY['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_COMPANY, [
                    'notification_id' => $notification->getId(),
                ]);

                return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
            }

            $users = [];
            foreach ($companies as $company) {
                foreach ($company->getUsers() as $user) {
                    $users[] = $user;
                }
            }

            if (empty($users)) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_USERS['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_USERS, [
                    'notification_id' => $notification->getId(),
                ]);

                return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
            }

            $deviceTokens = [];
            foreach ($users as $user) {
                $tokens = $user->getDeviceTokens()->toArray();
                $deviceTokens = array_merge($deviceTokens, $tokens);
            }
            if (empty($deviceTokens)) {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_NO_USERS_TOKENS['message']);
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_NO_USERS_TOKENS, [
                    'notification_id' => $notification->getId(),
                ]);

                return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
            }

            $title = $notification->getTitle();
            $message = $notification->getMessage();

            $maxRetries = 3;
            $failedTokens = $deviceTokens;

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                if (empty($batchResult['failed'])) {
                    break;
                }

                $batchResult = $pushNotificationService->sendBatchNotification($failedTokens, $title, $message);
                $failedTokens = $batchResult['failed'];

                if (!empty($failedTokens)) {
                    $this->addFlash('warning', 'Intento ' . $attempt . ': No se pudo enviar la notificación a algunos dispositivos.');
                }
            }

            if (empty($failedTokens)) {
                $this->addFlash('success', 'Notificaciónes enviada correctamente');
            } else {
                $this->addFlash('error', NotificationErrorCodes::NOTIFICATION_SEND_FAILED['message'] . implode(', ', $failedTokens));
                $applicationErrorService->createError(NotificationErrorCodes::NOTIFICATION_SEND_FAILED, [
                    'notification_id' => $notification->getId(),
                    'failed_tokens' => $failedTokens,
                ]);
            }

            return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
        }
    */
}