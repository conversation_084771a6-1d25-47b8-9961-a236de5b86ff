<?php

namespace App\Controller;

use App\Entity\Event;
use App\Entity\Beneficiary;
use App\Entity\UnreadMessage;
use App\Enum\Status;

use App\Repository\NotificationRepository;
use App\Repository\BeneficiaryRepository;
use App\Repository\EventRepository;
use App\Repository\CompanyRepository;
use App\Repository\UserRepository;
use App\Service\ConversationService;
use App\Service\TenantManager;

use App\Service\TopicService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mercure\Authorization;
use Symfony\Component\Mercure\Discovery;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManagerInterface;

#[Route('/{dominio}/dashboard')]
final class DashboardController extends AbstractController
{
    private TenantManager $tenantManager;

    public function __construct(
        TenantManager $tenantManager,
    )
    {
        $this->tenantManager = $tenantManager;
    }

    #[Route('', name: 'app_dashboard', methods: ['GET'])]
    public function index(
        string $dominio,
        EntityManagerInterface $entityManager,
        Request $request,
    ): Response {
        try {
            $this->tenantManager->setCurrentTenant($dominio);
            
            // Get the entity manager for the current tenant
            $em = $this->tenantManager->getEntityManager();

            // Get repositories
            $eventRepository = $em->getRepository(Event::class);
            $notificationRepository = $em->getRepository('App\Entity\Notification');
            $beneficiaryRepository = $em->getRepository(Beneficiary::class);
            $companyRepository = $em->getRepository('App\Entity\Company');
            $userRepository = $em->getRepository('App\Entity\User');

            // Get the current user
            $user = $this->getUser();

            // Check if user has ROLE_ADMIN
            $isAdmin = in_array('ROLE_ADMIN', $user->getRoles(), true);

            $companyCount = count($companyRepository->findBy(['status' => Status::ACTIVE]));
            $agremiadosCount = $userRepository->count([
                'role' => 1,
                'status' => Status::ACTIVE
            ]);

            if ($isAdmin) {
                // Admin sees all data
                $notificationCount = count($notificationRepository->findBy(['status' => Status::ACTIVE]));
                $beneficiaryCount = count($beneficiaryRepository->findBy(['status' => Status::ACTIVE]));
                $events = $eventRepository->findBy(['status' => Status::ACTIVE]);
            } else {
                // Regular user only sees data from their regions
                $userRegions = $user->getRegions();
                $regionIds = [];

                foreach ($userRegions as $region) {
                    $regionIds[] = $region->getId();
                }

                // Filter notifications by region
                $notifications = $notificationRepository->findBy(['status' => Status::ACTIVE]);
                $filteredNotifications = [];

                foreach ($notifications as $notification) {
                    // Check if any of the notification's companies are in the user's regions
                    $notificationCompanies = $notification->getCompanies();
                    foreach ($notificationCompanies as $company) {
                        $companyRegion = $company->getRegion();
                        if ($companyRegion && in_array($companyRegion->getId(), $regionIds)) {
                            $filteredNotifications[] = $notification;
                            break; // Found a match, no need to check other companies
                        }
                    }
                }

                $notificationCount = count($filteredNotifications);

                // Filter beneficiaries by region
                $beneficiaries = $beneficiaryRepository->findBy(['status' => Status::ACTIVE]);
                $filteredBeneficiaries = [];

                foreach ($beneficiaries as $beneficiary) {
                    $beneficiaryUser = $beneficiary->getUser();
                    if ($beneficiaryUser) {
                        $beneficiaryRegions = $beneficiaryUser->getRegions();
                        foreach ($beneficiaryRegions as $region) {
                            if (in_array($region->getId(), $regionIds)) {
                                $filteredBeneficiaries[] = $beneficiary;
                                break;
                            }
                        }
                    }
                }

                $beneficiaryCount = count($filteredBeneficiaries);

                // Filter events by region
                $allEvents = $eventRepository->findBy(['status' => Status::ACTIVE]);
                $events = [];

                foreach ($allEvents as $event) {
                    $eventCompanies = $event->getCompanies();
                    foreach ($eventCompanies as $company) {
                        $companyRegion = $company->getRegion();
                        if ($companyRegion && in_array($companyRegion->getId(), $regionIds)) {
                            $events[] = $event;
                            break;
                        }
                    }
                }
            }

            // Generate weekly usage data for administrators
            $weeklyUsageData = $this->generateAdminWeeklyUsageData();

            // Generate age distribution data by gender
            $ageDistributionData = $this->generateAgeDistributionData($beneficiaryRepository);

            $user = $this->getUser();

            $userRegions = $user->getRegions();
            $regionCompanies = [];
            foreach ($userRegions as $region) {
                foreach ($region->getCompanies() as $company) {
                    $regionCompanies[] = $company;
                }
            }

            $conversationsCount = count($em->getRepository(UnreadMessage::class)->findAll());

            return $this->render('dashboard/index.html.twig', [
                'notificationCount' => $notificationCount ?? 0,
                'beneficiaryCount' => $beneficiaryCount ?? 0,
                'events' => $events ?? [],
                'weeklyUsageData' => $weeklyUsageData,
                'ageDistributionData' => $ageDistributionData,
                'companyCount' => $companyCount,
                'agremiadosCount' => $agremiadosCount,
                'conversationsCount' => $conversationsCount,
            ]);
        } catch (\Exception $e) {
            throw $this->createNotFoundException('Tenant not found: ' . $e->getMessage());
        }
    }

    /**
     * Generates simulated weekly usage data for administrators
     *
     * @return array Weekly usage data
     */
    private function generateAdminWeeklyUsageData(): array
    {
        // In a real application, this would fetch data from a database
        // For now, we'll generate some random data to simulate admin usage
        return [
            'Monday' => rand(15, 30),
            'Tuesday' => rand(15, 30),
            'Wednesday' => rand(10, 20),
            'Thursday' => rand(20, 35),
            'Friday' => rand(15, 25),
            'Saturday' => rand(10, 20),
            'Sunday' => rand(5, 15)
        ];
    }

    /**
     * Generates age distribution data by gender from actual beneficiary data
     *
     * @param BeneficiaryRepository $beneficiaryRepository The repository for Beneficiary entities
     * @return array Age distribution data by gender
     */
    private function generateAgeDistributionData(BeneficiaryRepository $beneficiaryRepository): array
    {
        // Define age ranges
        $ageRanges = [
            '78-85' => ['min' => 78, 'max' => 85],
            '68-75' => ['min' => 68, 'max' => 75],
            '58-68' => ['min' => 58, 'max' => 68],
            '48-58' => ['min' => 48, 'max' => 58],
            '38-48' => ['min' => 38, 'max' => 48],
            '28-38' => ['min' => 28, 'max' => 38],
            '18-28' => ['min' => 18, 'max' => 28]
        ];

        // Initialize counts for each age range and gender
        $menCounts = array_fill_keys(array_keys($ageRanges), 0);
        $womenCounts = array_fill_keys(array_keys($ageRanges), 0);

        // Get all active beneficiaries
        $beneficiaries = $beneficiaryRepository->findBy(['status' => Status::ACTIVE]);

        $now = new \DateTime();

        // Count beneficiaries by age range and gender
        foreach ($beneficiaries as $beneficiary) {
            $birthday = $beneficiary->getBirthday();
            $gender = $beneficiary->getGender();

            if (!$birthday) {
                continue;
            }

            // Calculate age
            $age = $birthday->diff($now)->y;

            // Determine which age range this beneficiary falls into
            foreach ($ageRanges as $range => $limits) {
                if ($age >= $limits['min'] && $age <= $limits['max']) {
                    // Increment the appropriate counter based on gender
                    if (strtolower($gender) === 'masculino' || strtolower($gender) === 'hombre' || strtolower($gender) === 'm') {
                        $menCounts[$range]++;
                    } elseif (strtolower($gender) === 'femenino' || strtolower($gender) === 'mujer' || strtolower($gender) === 'f') {
                        $womenCounts[$range]++;
                    }
                    break;
                }
            }
        }

        // Convert counts to the format expected by the chart
        return [
            'categories' => array_keys($ageRanges),
            'men' => array_values($menCounts),
            'women' => array_values($womenCounts)
        ];
    }
}
