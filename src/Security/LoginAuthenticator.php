<?php

namespace App\Security;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\SecurityRequestAttributes;
use Symfony\Component\Security\Http\Util\TargetPathTrait;
use App\Service\TenantManager;

class LoginAuthenticator extends AbstractLoginFormAuthenticator
{
    use TargetPathTrait;

    public const LOGIN_ROUTE = 'app_login';

    private UrlGeneratorInterface $urlGenerator;
    private TenantManager $tenantManager;

    public function __construct(UrlGeneratorInterface $urlGenerator, TenantManager $tenantManager)
    {
        $this->urlGenerator = $urlGenerator;
        $this->tenantManager = $tenantManager;
    }

    public function authenticate(Request $request): Passport
    {
        $email = $request->getPayload()->getString('email');
        $tenant = $request->attributes->get('dominio');

        // Configurar el tenant antes de la autenticación
        $this->tenantManager->setCurrentTenant($tenant);

        $request->getSession()->set(SecurityRequestAttributes::LAST_USERNAME, $email);
        $request->getSession()->set('tenant', $tenant);

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($request->getPayload()->getString('password')),
            [
                new CsrfTokenBadge('authenticate', $request->getPayload()->getString('_csrf_token')),
                new RememberMeBadge(),
            ]
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        if ($targetPath = $this->getTargetPath($request->getSession(), $firewallName)) {
            return new RedirectResponse($targetPath);
        }

        $tenant = $request->attributes->get('dominio');
        
        // Asegurarse de que el tenant esté configurado después de la autenticación exitosa
        $this->tenantManager->setCurrentTenant($tenant);
        
        $roles = $token->getRoleNames();

        if (in_array('ROLE_ADMIN', $roles, true) || in_array('ROLE_USER', $roles, true) || in_array('ROLE_LIDER', $roles, true)) {
            return new RedirectResponse($this->urlGenerator->generate('app_dashboard', ['dominio' => $tenant]));
        }

        // Fallback to dashboard as default
        return new RedirectResponse($this->urlGenerator->generate('app_dashboard', ['dominio' => $tenant]));
    }

    protected function getLoginUrl(Request $request): string
    {
        $tenant = $request->attributes->get('dominio');
        return $this->urlGenerator->generate(self::LOGIN_ROUTE, ['dominio' => $tenant]);
    }
}
