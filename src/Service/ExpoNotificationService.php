<?php

namespace App\Service;

use App\Enum\ErrorCodes\PushNotificationErrorCodes;
use ExpoSDK\Expo;
use ExpoSDK\ExpoMessage;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Contracts\HttpClient\HttpClientInterface;


class ExpoNotificationService
{
    private ApplicationErrorService $applicationErrorService;
    private HttpClientInterface $httpClient;
    private string $expoAccessToken;

    public function __construct(ApplicationErrorService $applicationErrorService,
                                HttpClientInterface $httpClient,
                                string $expoAccessToken)
    {
        $this->applicationErrorService = $applicationErrorService;
        $this->httpClient = $httpClient;
        $this->expoAccessToken = $expoAccessToken;
    }
    /*public function sendExpoNotification(array $deviceTokens, string $title, string $message)
    {
        if (empty($deviceTokens)) {
            $this->applicationErrorService->createError(PushNotificationErrorCodes::PUSH_NOTIFICATION_NO_USERS_TOKENS);

            return ['success' => [], 'failed' => $deviceTokens];
        }

        $message = (new ExpoMessage())
            ->setTitle($title)
            ->setBody($message)
            ->playSound();

        try {

            (new Expo)->send($message)->to($deviceTokens)->push();

            return [
                'success' => true,
            ];
        } catch (\Exception $exception) {
            return [
                'success' => false,
                'message' => $exception->getMessage(),
            ];
        }
    }*/

    public function sendExpoNotification(array $deviceTokens, string $title, string $message): array
    {
        if (empty($deviceTokens)) {
            return ['success' => [], 'failed' => $deviceTokens];
        }

        $notifications = array_map(fn($token) => [
            'to' => $token,
            'title' => $title,
            'body' => $message,
            'sound' => 'default'
        ], $deviceTokens);

        try {
            $response = $this->httpClient->request('POST', 'https://exp.host/--/api/v2/push/send', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->expoAccessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $notifications,
            ]);

            $data = $response->toArray();
            return ['success' => true, 'response' => $data];
        } catch (\Throwable $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}