<?php
namespace App\Service;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Enum\ErrorCodes\ImagePathErrorCodes;
use App\Service\ApplicationErrorService;

class ImagePathService
{
    private RequestStack $requestStack;
    private ApplicationErrorService $applicationErrorService;
    private ParameterBagInterface $params;

    public function __construct(
        RequestStack $requestStack,
        ApplicationErrorService $applicationErrorService,
        ParameterBagInterface $params
    ) {
        $this->applicationErrorService = $applicationErrorService;
        $this->requestStack = $requestStack;
        $this->params = $params;
    }

    public function generateFullPath(?string $relativePath): ?string
    {
        if (!$relativePath) {
            $this->applicationErrorService->createError(ImagePathErrorCodes::IMAGE_PATH_SERVICE_NO_RELATIVE_PATH);

            return null;
        }

        $request = $this->requestStack->getCurrentRequest();
        $env = $this->params->get('app.env');

        // Para desarrollo, siempre usar las variables de entorno para evitar problemas de proxy
        if ($env === 'dev') {
            $baseUrl = $this->params->get('app.url.dev');
        } else {
            // En producción, intentar usar el request actual primero
            if ($request) {
                $baseUrl = $request->getSchemeAndHttpHost();
            } else {
                $baseUrl = $this->params->get('app.url.prod');
            }
        }

        return $baseUrl . '/uploads/' . $relativePath;
    }
}