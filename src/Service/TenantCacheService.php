<?php

namespace App\Service;

use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Symfony\Contracts\Cache\ItemInterface;
use Psr\Log\LoggerInterface;

class TenantCacheService
{
    private FilesystemAdapter $cache;
    private TenantManager $tenantManager;
    private LoggerInterface $logger;

    public function __construct(
        TenantManager $tenantManager,
        LoggerInterface $logger,
        string $cacheDir
    ) {
        $this->tenantManager = $tenantManager;
        $this->logger = $logger;
        $this->cache = new FilesystemAdapter('', 0, $cacheDir);
    }

    public function get(string $key, callable $callback, int $expiresAfter = 3600)
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        if (!$tenant) {
            throw new \RuntimeException('No tenant set for cache operation');
        }

        $cacheKey = sprintf('%s_%s', $tenant, $key);

        try {
            return $this->cache->get($cacheKey, function (ItemInterface $item) use ($callback, $expiresAfter) {
                $item->expiresAfter($expiresAfter);
                return $callback();
            });
        } catch (\Exception $e) {
            $this->logger->error('Cache error: ' . $e->getMessage(), [
                'tenant' => $tenant,
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return $callback();
        }
    }

    public function delete(string $key): bool
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        if (!$tenant) {
            throw new \RuntimeException('No tenant set for cache operation');
        }

        $cacheKey = sprintf('%s_%s', $tenant, $key);
        return $this->cache->delete($cacheKey);
    }

    public function clear(): bool
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        if (!$tenant) {
            throw new \RuntimeException('No tenant set for cache operation');
        }

        try {
            return $this->cache->clear();
        } catch (\Exception $e) {
            $this->logger->error('Cache clear error: ' . $e->getMessage(), [
                'tenant' => $tenant,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 