<?php

namespace App\Service;

use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;
use Psr\Log\LoggerInterface;

class TenantLoggerService implements LoggerInterface
{
    private array $loggers = [];
    private TenantManager $tenantManager;
    private string $logDir;

    public function __construct(
        TenantManager $tenantManager,
        string $logDir
    ) {
        $this->tenantManager = $tenantManager;
        $this->logDir = $logDir;
    }

    private function getLogger(): Logger
    {
        $tenant = $this->tenantManager->getCurrentTenant();
        if (!$tenant) {
            throw new \RuntimeException('No tenant set for logging operation');
        }

        if (!isset($this->loggers[$tenant])) {
            $logger = new Logger($tenant);
            
            $handler = new RotatingFileHandler(
                sprintf('%s/%s.log', $this->logDir, $tenant),
                10,
                Logger::DEBUG
            );
            
            $formatter = new LineFormatter(
                "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
                "Y-m-d H:i:s"
            );
            
            $handler->setFormatter($formatter);
            $logger->pushHandler($handler);
            
            $this->loggers[$tenant] = $logger;
        }

        return $this->loggers[$tenant];
    }

    public function emergency($message, array $context = []): void
    {
        $this->getLogger()->emergency($message, $context);
    }

    public function alert($message, array $context = []): void
    {
        $this->getLogger()->alert($message, $context);
    }

    public function critical($message, array $context = []): void
    {
        $this->getLogger()->critical($message, $context);
    }

    public function error($message, array $context = []): void
    {
        $this->getLogger()->error($message, $context);
    }

    public function warning($message, array $context = []): void
    {
        $this->getLogger()->warning($message, $context);
    }

    public function notice($message, array $context = []): void
    {
        $this->getLogger()->notice($message, $context);
    }

    public function info($message, array $context = []): void
    {
        $this->getLogger()->info($message, $context);
    }

    public function debug($message, array $context = []): void
    {
        $this->getLogger()->debug($message, $context);
    }

    public function log($level, $message, array $context = []): void
    {
        $this->getLogger()->log($level, $message, $context);
    }
} 