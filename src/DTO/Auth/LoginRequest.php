<?php
namespace App\DTO\Auth;

use Symfony\Component\Validator\Constraints as Assert;

class LoginRequest
{
    #[Assert\NotBlank(message: 'El correo electrónico es obligatorio.')]
    #[Assert\Email(message: 'El correo electrónico no es válido.')]
    #[Assert\Type(type: 'string', message: 'El correo electrónico debe ser una cadena de texto.')]
    public ?string $email = null;

    #[Assert\NotBlank(message: 'La contraseña es obligatoria.')]
    #[Assert\Length(
        min: 6,
        max: 50,
        minMessage: 'La contraseña debe tener al menos {{ limit }} caracteres.',
        maxMessage: 'La contraseña no puede tener más de {{ limit }} caracteres.'
    )]
    #[Assert\Type(type: 'string', message: 'La contraseña debe ser una cadena de texto.')]
    public ?string $password = null;
}