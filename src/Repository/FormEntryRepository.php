<?php

namespace App\Repository;

use App\Entity\FormEntry;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FormEntry>
 *
 * @method FormEntry|null find($id, $lockMode = null, $lockVersion = null)
 * @method FormEntry|null findOneBy(array $criteria, array $orderBy = null)
 * @method FormEntry[]    findAll()
 * @method FormEntry[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormEntryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FormEntry::class);
    }

    public function save(FormEntry $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FormEntry $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}