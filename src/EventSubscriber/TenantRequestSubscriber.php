<?php

namespace App\EventSubscriber;

use App\Service\TenantManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TenantRequestSubscriber implements EventSubscriberInterface
{
    private $tenantManager;

    public function __construct(TenantManager $tenantManager)
    {
        $this->tenantManager = $tenantManager;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            // Ejecutar después del enrutamiento (32) pero antes de la autenticación (8)
            KernelEvents::REQUEST => ['onKernelRequest', 31],
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        $request = $event->getRequest();
        
        // Omitir solicitudes de preflight y rutas que no son de API
        if ($request->isMethod('OPTIONS') || !str_starts_with($request->getPathInfo(), '/')) {
            return;
        }

        // Obtener el tenant de los parámetros de la ruta
        $dominio = $request->attributes->get('dominio');
        
        // Omitir si la ruta no requiere tenant
        if ($dominio === null) {
            return;
        }

        // Validación básica
        if (empty($dominio) || !is_string($dominio)) {
            throw new BadRequestHttpException('El parámetro tenant es inválido');
        }

        try {
            $this->tenantManager->setCurrentTenant($dominio);
        } catch (NotFoundHttpException $e) {
            throw new NotFoundHttpException(sprintf(
                'Tenant "%s" no es válido. Los tenants permitidos son: %s',
                $dominio,
                implode(', ', $this->tenantManager->getAllowedTenants())
            ));
        } catch (\Exception $e) {
            throw new BadRequestHttpException('Error al configurar el tenant: ' . $e->getMessage());
        }
    }
}