nelmio_cors:
    defaults:
        allow_credentials: true
        origin_regex: true
        allow_origin: ['*']
        allow_headers: ['Content-Type', 'Authorization']
        allow_methods: ['POST', 'GET', 'OPTIONS']
        expose_headers: ['Authorization']
        max_age: 3600
    paths:
        '^/api/':
            allow_origin: ['*']
            allow_headers: ['Content-Type', 'Authorization', 'X-Requested-With']
            allow_methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
            max_age: 3600
