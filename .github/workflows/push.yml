name: Deploy NSMAX

on:
  push:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: antoonio/asnmx:${{ github.sha }}
  WORK_DIR: /home/<USER>/wrkdirs/asnmx
  SSH_USER: antoonio
  SSH_HOST: ***************
  APP_URL_DEV: http://***************:8004
  APP_URL_PROD: https://sindicato.grupooptimo.mx

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure Secrets
        env:
          GHCR_PAT_SNMX: ${{ secrets.GHCR_PAT_SNMX }}
        run: |
          echo "GHCR_PAT_SNMX=$GHCR_PAT_SNMX" >> $GITHUB_ENV

      - name: Ensure Docker network exists
        run: |
          docker network inspect database >/dev/null 2>&1 || docker network create database

      - name: Update Composer dependencies
        run: |
          docker run --rm \
            -v $(pwd):/app \
            -w /app \
            php:8.2-cli \
            sh -c "apt-get update && \
                  apt-get install -y \
                    libfreetype6-dev \
                    libjpeg62-turbo-dev \
                    libpng-dev \
                    libzip-dev \
                    git \
                    unzip && \
                  docker-php-ext-configure gd --with-freetype --with-jpeg && \
                  docker-php-ext-install gd zip && \
                  git config --global --add safe.directory /app && \
                  curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer && \
                  composer update --no-scripts && \
                  composer install --no-scripts"

      - name: Build Docker image
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }} -f .docker/Dockerfile .

      - name: Log in to GitHub Container Registry
        run: echo "${{ env.GHCR_PAT_SNMX }}" | docker login ghcr.io -u ${{ env.SSH_USER }} --password-stdin

      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Create docker.env file
        run: |
          mkdir -p .docker/env
          cat << EOF > .docker/env/docker.env
          APP_ENV=prod
          APP_DEBUG=1
          APP_SECRET=${{ secrets.APP_SECRET || 'msc2025*' }}
          DATABASE_URL=${{ secrets.DATABASE_URL_PROD || 'mysql://root:root@mysql:3306/msc-app-ts' }}
          DATABASE_URL_TS=${{ secrets.DATABASE_URL_TS_PROD || 'mysql://root:root@mysql:3306/msc-app-ts' }}
          DATABASE_URL_SNT=${{ secrets.DATABASE_URL_SNT_PROD || 'mysql://root:root@mysql:3306/msc-app-ctm' }}
          LOCK_DSN=${{ secrets.LOCK_DSN_PROD || 'mysql://root:root@mysql:3306/msc-app-ts' }}
          EXPO_ACCESS_TOKEN=${{ secrets.EXPO_ACCESS_TOKEN || 'ZlVS45CLDK9cZaLgkSZhjHg-N6svEDQCBGOzmZKW' }}
          MERCURE_URL=${{ secrets.MERCURE_URL || 'http://mercure/.well-known/mercure' }}
          MERCURE_PUBLIC_URL=${{ 'https://sindicato.grupooptimo.mx/.well-known/mercure' }}
          MERCURE_JWT_SECRET=${{ secrets.MERCURE_JWT_SECRET || 'SANFEAFcV9JQTKlutJx3cAtudhAGHHkBCSdt3vDxINM=' }}
          CORS_ALLOW_ORIGIN=${{ secrets.CORS_ALLOW_ORIGIN_PROD || '^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$' }}
          JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
          JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
          CREDENTIAL_JWT_TTL=300
          JWT_PASSPHRASE=${{ secrets.JWT_PASSPHRASE || '****************************************************************' }}
          FIREBASE_CREDENTIALS=%kernel.project_dir%/config/firebase/firebase_credentials.json
          TWILIO_ACCOUNT_SID=${{ secrets.TWILIO_ACCOUNT_SID }}
          TWILIO_AUTH_TOKEN=${{ secrets.TWILIO_AUTH_TOKEN }}
          TWILIO_WHATSAPP_NUMBER=${{ secrets.TWILIO_WHATSAPP_NUMBER }}
          TWILIO_PHONE_NUMBER=${{ secrets.TWILIO_PHONE_NUMBER }}
          WHATSAPP_API_URL=
          WHATSAPP_ACCESS_TOKEN=
          WHATSAPP_PHONE_NUMBER_ID=
          MAILER_DSN=${{ secrets.MAILER_DSN || 'smtp://<EMAIL>:<EMAIL>:587?encryption=tls&auth_mode=login' }}
          SMTP_USERNAME=${{ secrets.SMTP_USERNAME || '<EMAIL>' }}
          SMTP_PASSWORD=${{ secrets.SMTP_PASSWORD || 'zyxacjfbfwjntkie' }}
          APP_URL_DEV=${{ env.APP_URL_DEV }}
          APP_URL_PROD=${{ env.APP_URL_PROD }}
          EOF

      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ env.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Sync Environment File
        run: |
          scp -o StrictHostKeyChecking=no \
          .docker/env/docker.env \
          ${{ env.SSH_USER }}@${{ env.SSH_HOST }}:${{ env.WORK_DIR }}/.docker/env/docker.env

      - name: Sync Backup Script
        run: |
          # Crear directorio de scripts en el servidor si no existe
          ssh -o StrictHostKeyChecking=no ${{ env.SSH_USER }}@${{ env.SSH_HOST }} "mkdir -p ${{ env.WORK_DIR }}/scripts"

          # Copiar script de backup
          scp -o StrictHostKeyChecking=no \
          scripts/pre-deployment-backup.sh \
          ${{ env.SSH_USER }}@${{ env.SSH_HOST }}:${{ env.WORK_DIR }}/scripts/pre-deployment-backup.sh

      - name: Deploy to Server
        run: |
          ssh -o StrictHostKeyChecking=no ${{ env.SSH_USER }}@${{ env.SSH_HOST }} << 'EOF'
          set -euxo pipefail

          echo "🛡️ EJECUTANDO BACKUP PRE-DESPLIEGUE..."
          # Asegurar que el script tenga permisos de ejecución
          chmod +x ${{ env.WORK_DIR }}/scripts/pre-deployment-backup.sh

          # Ejecutar backup antes del despliegue
          ${{ env.WORK_DIR }}/scripts/pre-deployment-backup.sh || {
            echo "⚠️ Backup falló, pero continuando con despliegue..."
          }
          echo "=== LIMPIEZA AGRESIVA DE DOCKER ==="
          # Parar contenedor anterior si existe
          docker stop asnmx || true
          docker rm asnmx || true

          # Limpiar imágenes no utilizadas (excepto servicios persistentes)
          docker system prune -a -f --filter "label!=persistent=true"

          # Limpiar volúmenes huérfanos (EXCLUYENDO volúmenes de datos persistentes)
          echo "🔍 Verificando volúmenes antes de limpiar..."
          docker volume ls
          # Solo limpiar volúmenes que NO sean de datos persistentes
          docker volume prune -f --filter "label!=persistent=true" || true
          echo "⚠️  PROTECCIÓN: Volúmenes mysql_data, mercure_data y mercure_config se mantienen intactos"

          # Limpiar imágenes dangling
          docker image prune -a -f

          # Mostrar espacio disponible
          df -h
          echo "Espacio liberado. Continuando con deployment..."

          echo "=== VERIFICANDO SERVICIOS PERSISTENTES ==="
          # Crear red si no existe
          docker network inspect database >/dev/null 2>&1 || docker network create database

          # Verificar y crear MySQL si no existe
          if ! docker ps -q -f name=mysql | grep -q .; then
            echo "⚠️  MySQL no encontrado. Creando MySQL persistente..."
            echo "🚨 IMPORTANTE: Ejecuta el script setup-server.sh para servicios persistentes"

            # Verificar si el volumen ya existe (datos previos)
            if docker volume inspect mysql_data >/dev/null 2>&1; then
              echo "✅ Volumen mysql_data existente detectado - Los datos se preservarán"
            else
              echo "🆕 Creando nuevo volumen mysql_data"
              docker volume create mysql_data
            fi

            docker run -d \
              --name mysql \
              --network database \
              --restart unless-stopped \
              --label persistent=true \
              -e MYSQL_DATABASE=msc-app-ts \
              -e MYSQL_ROOT_PASSWORD=root \
              -e MYSQL_ROOT_HOST=% \
              -v mysql_data:/var/lib/mysql \
              -p 33306:3306 \
              mysql:8.0.35 \
              --default-authentication-plugin=mysql_native_password \
              --bind-address=0.0.0.0

            # Esperar a que MySQL esté listo
            echo "⏳ Esperando a que MySQL esté listo..."
            for i in {1..60}; do
              if docker exec mysql mysqladmin ping -h"localhost" -uroot -proot --silent 2>/dev/null; then
                echo "✅ MySQL está listo!"
                break
              fi
              echo "Esperando a MySQL... (intento $i/60)"
              sleep 3
            done

            # Crear base de datos adicional
            echo "🗄️ Creando bases de datos..."
            docker exec mysql mysql -uroot -proot -e "CREATE DATABASE IF NOT EXISTS \`msc-app-ctm\`;" || true
            docker exec mysql mysql -uroot -proot -e "SHOW DATABASES;" || true
          else
            echo "✅ MySQL ya está ejecutándose"
          fi

          # Verificar y crear phpMyAdmin si no existe
          if ! docker ps -q -f name=phpmyadmin | grep -q .; then
            echo "🔧 Iniciando phpMyAdmin..."
            docker run -d \
              --name phpmyadmin \
              --network database \
              --restart unless-stopped \
              --label persistent=true \
              -e PMA_HOST=mysql \
              -e UPLOAD_LIMIT=100M \
              -p 8080:80 \
              phpmyadmin/phpmyadmin
          else
            echo "✅ phpMyAdmin ya está ejecutándose"
          fi

          mkdir -p ${{ env.WORK_DIR }}/.docker/{jwt,env}
          chmod 750 ${{ env.WORK_DIR }}/.docker

          docker logout ghcr.io || true
          echo "${{ env.GHCR_PAT_SNMX }}" | docker login ghcr.io -u ${{ env.SSH_USER }} --password-stdin

          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

          docker stop asnmx || true
          docker rm asnmx || true

          docker run -d \
            --network database \
            -p 8004:80 \
            --name asnmx \
            --restart unless-stopped \
            --env-file ${{ env.WORK_DIR }}/.docker/env/docker.env \
            -v ${{ env.WORK_DIR }}/public/uploads:/var/www/html/public/uploads \
            -v ${{ env.WORK_DIR }}/.docker/jwt:/var/www/html/config/jwt \
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

          echo "=== VERIFICANDO APLICACIÓN ==="
          # El entrypoint ya maneja las migraciones automáticamente
          # Solo verificamos que la aplicación esté funcionando
          sleep 10
          echo "✅ Aplicación desplegada. Las migraciones se ejecutan automáticamente en el entrypoint."

          echo "=== LIMPIEZA POST-DEPLOYMENT ==="
          # Limpiar imágenes viejas después del deployment exitoso
          docker image prune -a -f --filter "until=24h"

          # Mostrar estado final del espacio
          echo "📊 Estado final del espacio en disco:"
          df -h
          echo "📦 Imágenes Docker actuales:"
          docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
          EOF
