{% extends 'base-login.html.twig' %}

{% set dominio = app.request.get('dominio', 'ts') %}

{% block title %}Verificar código{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .logo-login {
            animation: pulse 2s infinite;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4))
                    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))
                    drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
{% endblock %}

{% block body %}
<body class="bg-dark text-white d-flex align-items-center justify-content-center vh-100">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-7 col-lg-6">
        <div class="login-wrapper p-4 text-white">
          <div class="text-center mb-4 d-flex flex-column align-items-center">
            <img src="/images/logos/logoTS.svg" alt="Logo" class="logo-login mb-2" />
            <h2 class="login-title">VERIFICAR CÓDIGO</h2>
          </div>

          <form id="verifyCodeForm" method="post" action="{{ path('verify_code', {'dominio': dominio}) }}">
            <div class="form-group mb-3 d-flex flex-column align-items-center">
              <label for="verificationCode" class="form-label fw-light text-login">CÓDIGO DE VERIFICACIÓN</label>
              <input type="text" name="verification_code" id="verificationCode" class="form-control rounded-pill input-field" required>
            </div>

            <div class="form-group mb-3 d-flex flex-column align-items-center">
              <label for="newPassword" class="form-label fw-light text-login">NUEVA CONTRASEÑA</label>
              <input type="password" name="new_password" id="newPassword" class="form-control rounded-pill input-field" required>
            </div>

            <div class="d-flex flex-column align-items-center">
              <button class="btn btn-lg btn-primary rounded-pill btn-login" type="submit">
                CAMBIAR CONTRASEÑA
              </button>
              <a href="{{ path('forget_password', {'dominio': dominio}) }}" class="btn btn-link text-white mt-2">Volver</a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
document.getElementById('verifyCodeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    fetch(this.action, {
        method: 'POST',
        body: new FormData(this),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Contraseña actualizada correctamente');
            window.location.href = '{{ path('app_login', {'dominio': dominio}) }}';
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al procesar la solicitud');
    });
});
</script>
{% endblock %}
