{% extends 'base-login.html.twig' %}

{% set dominio = app.request.get('dominio', 'ts') %}

{% block title %}Recuperar contraseña{% endblock %}

{% block body %}
<body class="bg-light text-dark d-flex align-items-center justify-content-center vh-100">
<div class="container-fluid p-0 m-0" style="height:100vh;">
    <div class="row g-0 h-100 flex-md-row flex-column-reverse">
        <div class="col-md-6 d-flex align-items-center justify-content-center left-panel">
            <img src="/images/logos/logoTS.svg" alt="Logo TS" class="logo-ts mb-3"
                 style="max-width:260px; width:60vw; min-width:120px; filter: drop-shadow(0 6px 32px rgba(13,110,253,0.18));">
        </div>

        <div class="col-md-6 d-flex align-items-center justify-content-center right-panel bg-white shadow-lg">
            <div class="w-100" style="max-width:400px; margin: 2rem auto;">
                <h2 class="mb-4 fw-semibold text-center" style="color:#0d6efd;">Recuperar Contraseña</h2>

                <div id="step1">
                    <form id="forgetPasswordForm" method="post" action="{{ path('forget_password', {'dominio': dominio}) }}"
                          autocomplete="off" novalidate>
                        <div class="mb-4">
                            <label for="inputEmail" class="form-label small text-secondary">Correo Electrónico</label>
                            <input type="email" name="email" id="inputEmail" class="form-control custom-input" required
                                   placeholder="Ingresa tu correo">
                            <div class="invalid-feedback text-center" id="emailError"></div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg custom-btn" id="sendCodeBtn">
                                <span id="sendCodeText">Enviar Código</span>
                                <span id="sendCodeSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                            <a href="{{ path('app_login', {'dominio': dominio}) }}" class="btn btn-link text-secondary mt-2">Volver al inicio de sesión</a>
                        </div>
                    </form>
                </div>

                <div id="step2" style="display:none;">
                    <form id="verifyCodeForm" method="post" action="{{ path('verify_code', {'dominio': dominio}) }}"
                          autocomplete="off" novalidate>
                        <div class="mb-4">
                            <label for="verificationCode" class="form-label small text-secondary">Código de Verificación</label>
                            <input type="text" name="verification_code" id="verificationCode" class="form-control custom-input"
                                   required maxlength="6" pattern="\\d{6}" placeholder="Código de 6 dígitos">
                            <div class="invalid-feedback text-center" id="codeError"></div>
                        </div>
                        <div class="mb-4 position-relative">
                            <label for="newPassword" class="form-label small text-secondary">Nueva Contraseña</label>
                            <div class="input-group custom-input-group position-relative">
                                <input type="password" name="new_password" id="newPassword"
                                       class="form-control custom-input pr-5" required minlength="8"
                                       placeholder="Mínimo 8 caracteres, 1 mayúscula, 1 número, 1 símbolo">
                                <button type="button" class="password-toggle-eye" tabindex="-1"
                                        aria-label="Mostrar/Ocultar contraseña" data-target="newPassword"
                                        style="position:absolute;right:1rem;top:50%;transform:translateY(-50%);background:transparent;border:none;padding:0;z-index:2;">
                                    <span class="fa fa-eye"></span>
                                </button>
                            </div>
                            <div class="invalid-feedback text-center" id="passwordError"></div>
                        </div>
                        <div class="mb-4 position-relative">
                            <label for="repeatPassword" class="form-label small text-secondary">Confirmar Nueva Contraseña</label>
                            <div class="input-group custom-input-group position-relative">
                                <input type="password" name="repeat_password" id="repeatPassword"
                                       class="form-control custom-input pr-5" required
                                       placeholder="Repite la nueva contraseña">
                                <button type="button" class="password-toggle-eye" tabindex="-1"
                                        aria-label="Mostrar/Ocultar contraseña" data-target="repeatPassword"
                                        style="position:absolute;right:1rem;top:50%;transform:translateY(-50%);background:transparent;border:none;padding:0;z-index:2;">
                                    <span class="fa fa-eye"></span>
                                </button>
                            </div>
                            <div class="invalid-feedback text-center" id="repeatPasswordError"></div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg custom-btn" id="changePasswordBtn">
                                <span id="changePasswordText">Cambiar Contraseña</span>
                                <span id="changePasswordSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                            <button type="button" class="btn btn-link text-secondary mt-2" onclick="showStep1()">Volver</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    const newPasswordInput = document.getElementById('newPassword');
    const repeatPasswordInput = document.getElementById('repeatPassword');

    function showStep1() {
        document.getElementById('step1').style.display = 'block';
        document.getElementById('step2').style.display = 'none';
    }

    function showStep2() {
        document.getElementById('step1').style.display = 'none';
        document.getElementById('step2').style.display = 'block';
    }

    document.querySelectorAll('.password-toggle-eye').forEach(btn => {
        btn.addEventListener('click', function () {
            const input = document.getElementById(this.dataset.target);
            const icon = this.querySelector('span');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    document.getElementById('forgetPasswordForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const btn = document.getElementById('sendCodeBtn');
        const spinner = document.getElementById('sendCodeSpinner');
        const text = document.getElementById('sendCodeText');
        btn.disabled = true;
        spinner.classList.remove('d-none');
        text.classList.add('d-none');

        fetch(this.action, {
            method: 'POST',
            body: new FormData(this),
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
            .then(response => response.json())
            .then(data => {
                btn.disabled = false;
                spinner.classList.add('d-none');
                text.classList.remove('d-none');
                if (data.success) {
                    Swal.fire({ icon: 'success', title: '¡Código enviado!', text: data.message, confirmButtonText: 'Continuar' }).then(showStep2);
                } else {
                    Swal.fire({ icon: 'error', title: 'Error', text: data.message });
                }
            });
    });

    document.getElementById('verifyCodeForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const password = newPasswordInput.value;
        const repeat = repeatPasswordInput.value;
        const regex = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).{8,}$/;
        let valid = true;

        if (!regex.test(password)) {
            Swal.fire({ icon: 'error', title: 'Error', text: 'La contraseña debe tener mínimo 8 caracteres, 1 mayúscula, 1 número y 1 símbolo.' });
            valid = false;
        }
        if (password !== repeat) {
            Swal.fire({ icon: 'error', title: 'Error', text: 'Las contraseñas no coinciden.' });
            valid = false;
        }

        if (!valid) return;

        const btn = document.getElementById('changePasswordBtn');
        const spinner = document.getElementById('changePasswordSpinner');
        const text = document.getElementById('changePasswordText');
        btn.disabled = true;
        spinner.classList.remove('d-none');
        text.classList.add('d-none');

        fetch(this.action, {
            method: 'POST',
            body: new FormData(this),
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
            .then(response => response.json())
            .then(data => {
                btn.disabled = false;
                spinner.classList.add('d-none');
                text.classList.remove('d-none');
                if (data.success) {
                    Swal.fire({ icon: 'success', title: '¡Contraseña actualizada!', text: data.message, confirmButtonText: 'Iniciar sesión' })
                        .then(() => { window.location.href = "{{ path('app_login', {'dominio': dominio}) }}"; });
                } else {
                    Swal.fire({ icon: 'error', title: 'Error', text: data.message });
                }
            });
    });
</script>
{% endblock %}
