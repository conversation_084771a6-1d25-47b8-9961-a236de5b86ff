{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Empresas{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">EMPRESAS</h1>
        </div>
    </section>

    <div class="container text-center">
        <div class="row">
            <div class="col-12 d-flex justify-content-between my-3">
                <div class="col-3 new-user">
                    <a href="{{ path('app_company_new', {'dominio': dominio}) }}" class="btn-g fw-bold ">DAR DE ALTA</a>
                </div>

                <div class="d-flex align-items-center">
                    <img src="{{ asset('images/filter.svg') }}" alt="Filtro" class="icon-sntiasg me-2">
                    <input type="text" id="filterTitle" class="form-control w-auto" placeholder="Buscar empresa por nombre...">
                </div>
            </div>
        </div>

        <div class="table-container table-responsive px-4">
            <table class="styled-table table-striped table-bordered text-center">
                <thead class="table-primary text-dark">
                <tr>
                    <th>NOMBRE</th>
                    <th>ACCIONES</th>
                </tr>
                </thead>
                <tbody>
                {% for company in companies %}
                    <tr data-title="{{ company.name |lower }}">
                        <td>{{ company.name }}</td>
                        <td>
                            <a href="{{ path('app_company_show', {'id': company.id, 'dominio': dominio}) }}" class="btn-v fw-bold">VER</a>
                            <a href="{{ path('app_company_edit', {'id': company.id, 'dominio': dominio}) }}" class="btn-e fw-bold">EDITAR</a>
                            {{ include('company/_delete_form.html.twig', {'button_label': 'ELIMINAR', 'dominio': dominio}) }}
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="2">SIN EMPRESAS DISPONIBLES</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}


{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const input = document.getElementById('filterTitle');
            const rows = document.querySelectorAll('tbody tr[data-title]');
            const resultText = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            input.addEventListener('input', function () {
                const searchTerm = input.value.toLowerCase().trim();
                let count = 0;

                rows.forEach(row => {
                    const title = row.getAttribute('data-title');
                    const visible = title.includes(searchTerm);
                    row.style.display = visible ? '' : 'none';
                    if (visible) count++;
                });

                resultText.textContent = `Mostrando ${count} region${count === 1 ? 'o' : 'es'}.`;
                noResults.style.display = count === 0 ? 'block' : 'none';
            });
        });
    </script>
{% endblock %}
