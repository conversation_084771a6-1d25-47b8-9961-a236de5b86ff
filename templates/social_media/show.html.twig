{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Ver Post - {{ social_media.title }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .detail-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 2rem 0;
        }

        .detail-header {
            border-bottom: 2px solid #1B283D;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .detail-title {
            color: #1B283D;
            font-weight: 700;
            font-size: 2rem;
            margin: 0;
        }

        .detail-row {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1B283D;
        }

        .detail-label {
            font-weight: 600;
            color: #1B283D;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
        }

        .detail-value {
            font-size: 1.1rem;
            color: #333;
            margin: 0;
        }

        .social-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .platform-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .platform-facebook { background: #1877f2; color: white; }
        .platform-instagram { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); color: white; }
        .platform-twitter { background: #1da1f2; color: white; }
        .platform-youtube { background: #ff0000; color: white; }
        .platform-linkedin { background: #0077b5; color: white; }
        .platform-default { background: #6c757d; color: white; }

        .url-link {
            color: #1B283D;
            text-decoration: none;
            word-break: break-all;
            transition: color 0.3s ease;
        }

        .url-link:hover {
            color: #0d6efd;
            text-decoration: underline;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #dee2e6;
        }

        .btn-custom {
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn-back:hover {
            background: #5a6268;
            color: white;
            transform: translateY(-2px);
        }

        .btn-edit {
            background: #ffc107;
            color: #212529;
        }

        .btn-edit:hover {
            background: #e0a800;
            color: #212529;
            transform: translateY(-2px);
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-custom {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">DETALLE POST REDES SOCIALES</h1>
        </div>
    </section>

    <div class="container py-4">
        <div class="detail-card">
            <div class="detail-header">
                <h1 class="detail-title">{{ social_media.title }}</h1>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Empresa</div>
                        <p class="detail-value">{{ social_media.company.name }}</p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Plataforma</div>
                        <p class="detail-value">
                            <span class="platform-badge platform-{{ social_media.platform|lower }}">
                                {{ social_media.platform }}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="col-12">
                    <div class="detail-row">
                        <div class="detail-label">Descripción</div>
                        <p class="detail-value">{{ social_media.description|nl2br }}</p>
                    </div>
                </div>

                <div class="col-12">
                    <div class="detail-row">
                        <div class="detail-label">URL del Post</div>
                        <p class="detail-value">
                            <a href="{{ social_media.url }}" target="_blank" class="url-link">
                                <i class="fas fa-external-link-alt me-2"></i>{{ social_media.url }}
                            </a>
                        </p>
                    </div>
                </div>

                {% if social_media.image %}
                <div class="col-12">
                    <div class="detail-row">
                        <div class="detail-label">Imagen</div>
                        <div class="text-center">
                            <img src="{{ asset('uploads/' ~ social_media.image) }}" 
                                 alt="{{ social_media.title }}" 
                                 class="social-image">
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Fecha de Inicio</div>
                        <p class="detail-value">
                            <i class="fas fa-calendar-alt me-2"></i>
                            {{ social_media.startDate|date('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Fecha de Fin</div>
                        <p class="detail-value">
                            <i class="fas fa-calendar-alt me-2"></i>
                            {{ social_media.endDate|date('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Fecha de Creación</div>
                        <p class="detail-value">
                            <i class="fas fa-clock me-2"></i>
                            {{ social_media.createdAt|date('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="detail-row">
                        <div class="detail-label">Última Actualización</div>
                        <p class="detail-value">
                            <i class="fas fa-clock me-2"></i>
                            {{ social_media.updatedAt|date('d/m/Y H:i') }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="{{ path('app_social_media_index', {'dominio': dominio}) }}" class="btn-custom btn-back">
                    <i class="fas fa-arrow-left"></i>
                    Volver
                </a>

                <a href="{{ path('app_social_media_edit', {'id': social_media.id, 'dominio': dominio}) }}" class="btn-custom btn-edit">
                    <i class="fas fa-edit"></i>
                    Editar
                </a>

                <button type="button" class="btn-custom btn-delete" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i>
                    Eliminar
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de confirmación para eliminar -->
    <form id="deleteForm" method="post" action="{{ path('app_social_media_delete', {'id': social_media.id, 'dominio': dominio}) }}" style="display: none;">
        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ social_media.id) }}">
    </form>

    <script>
        function confirmDelete() {
            if (confirm('¿Estás seguro de que deseas eliminar este post de redes sociales? Esta acción no se puede deshacer.')) {
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
{% endblock %}
