{{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}
<style>
    .form-control {
        border-radius: 8px;
        padding: 10px;
        font-size: 1rem;
    }
</style>

<div class="row">
    <div class="col-md-6 mb-3">
        {{ form_label(form.company) }}
        {{ form_widget(form.company, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.company) }}
    </div>

    <div class="col-md-6 mb-3">
        {{ form_label(form.platform) }}
        {{ form_widget(form.platform, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.platform) }}
    </div>

    <div class="col-md-6 mb-3">
        {{ form_label(form.title) }}
        {{ form_widget(form.title, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.title) }}
    </div>

    <div class="col-md-6 mb-3">
        {{ form_label(form.url) }}
        {{ form_widget(form.url, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.url) }}
    </div>

    <div class="col-12 mb-3">
        {{ form_label(form.description) }}
        {{ form_widget(form.description, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.description) }}
    </div>

    <div class="col-md-6 mb-3">
        {{ form_label(form.startDate) }}
        {{ form_widget(form.startDate, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.startDate) }}
    </div>

    <div class="col-md-6 mb-3">
        {{ form_label(form.endDate) }}
        {{ form_widget(form.endDate, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.endDate) }}
    </div>

    <div class="col-12 mb-4">
        {{ form_label(form.image) }}
        {{ form_widget(form.image, {'attr': {'class': 'form-control'}}) }}
        {{ form_errors(form.image) }}
    </div>
</div>

<div class="d-flex justify-content-end">
    <button type="submit" class="btn btn-primary">
        <i class="bi bi-upload"></i> Publicar
    </button>
</div>

{{ form_end(form) }}