{% extends 'base.html.twig' %}

{% block title %}Editar Red Social{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .form-container {
            max-width: 960px;
            margin: 0 auto;
        }
        .form-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        .page-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        .btn-back {
            color: #6c757d;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .btn-back:hover {
            color: #343a40;
            transform: translateX(-2px);
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid #ced4da;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        .btn-save {
            background-color: #007bff;
            color: white;
            border-radius: 20px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-save:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .select2-container .select2-selection--single {
            height: calc(1.5em + 1.5rem + 2px);
            padding: 0.75rem;
            border-radius: 8px;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="container py-4">
        <div class="form-container">

            <section class="header-sntiasg-b">
                <div class="container-fluid container-header">
                    <h1 class="title-sntiasg">EDITAR</h1>
                </div>
            </section>

            <br>

            <div class="form-card">
                <h1 class="page-title">Editar Red Social</h1>
                
                {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                    <div class="form-section">
                        <div class="row g-3">
                            <div class="col-md-6">
                                {{ form_row(form.company, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                            <div class="col-md-6">
                                {{ form_row(form.title, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="row g-3">
                            <div class="col-12">
                                {{ form_row(form.description, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                            <div class="col-md-6">
                                {{ form_row(form.url, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                            <div class="col-md-6">
                                {{ form_row(form.platform, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="row g-3">
                            <div class="col-12">
                                {% if form.vars.value.image %}
                                    <div class="mb-3">
                                        <img src="{{ asset('uploads/' ~ form.vars.value.image) }}" alt="Imagen actual" style="max-width: 200px; border-radius: 8px;">
                                    </div>
                                {% endif %}
                                {{ form_row(form.image, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                            <div class="col-md-6">
                                {{ form_row(form.startDate, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                            <div class="col-md-6">
                                {{ form_row(form.endDate, {
                                    'label_attr': {'class': 'form-label'},
                                    'attr': {'class': 'form-control'}
                                }) }}
                            </div>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>Guardar cambios
                        </button>
                    </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar Select2 para los selectores
            if ($.fn.select2) {
                $('select').select2({
                    theme: 'bootstrap4',
                    width: '100%'
                });
            }
        });
    </script>
{% endblock %}

