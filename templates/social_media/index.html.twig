{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Social Media{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Personalización de la scrollbar */
        .table-container::-webkit-scrollbar {
            width: 14px; /* Ancho de la scrollbar vertical */
            height: 14px; /* Altura de la scrollbar horizontal */
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1; /* Color de fondo de la pista */
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #1B283D; /* Color principal que solicitaste */
            border-radius: 10px;
            border: 3px solid #f1f1f1;
            transition: background 0.3s ease;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #101b2d; /* Color más oscuro al pasar el mouse */
        }

        .table-container::-webkit-scrollbar-corner {
            background: #f1f1f1; /* Color para la esquina */
        }

        /* Para Firefox */
        .table-container {
            scrollbar-width: thin;
            scrollbar-color: #1B283D #f1f1f1;
        }

        /* Estilos adicionales para mejorar la tabla */
        .table-container {
            max-height: 70vh; /* Altura máxima con scroll */
            overflow: auto;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .styled-table {
            min-width: 1000px; /* Ancho mínimo para evitar compresión */
        }

        .styled-table th {
            background-color: #1B283D;
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .styled-table tbody tr:hover {
            background-color: rgba(61, 121, 30, 0.05);
        }

        .social-url {
            color: #1B283D;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .social-url:hover {
            color: #1B283D;
            text-decoration: underline;
        }

        .action-btn {
            transition: all 0.3s ease;
            border-radius: 6px;
            font-weight: 500;
            margin: 0 2px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-group .action-btn {
            border-radius: 0;
        }

        .btn-group .action-btn:first-child {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .btn-group .action-btn:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }

        /* Responsive para botones de acción */
        @media (max-width: 768px) {
            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .action-btn {
                border-radius: 6px !important;
                margin: 2px 0;
                font-size: 0.8rem;
                padding: 0.25rem 0.5rem;
            }
        }
    </style>
{% endblock %}

{% block body %}

    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">POSTS REDES SOCIALES</h1>
        </div>
    </section>

    <div class="row">
        <div class="col-12 btn-filter d-flex justify-content-between my-3">
            <div class="col-3 new-user-admin">
                <a href="{{ path('app_social_media_new', {'dominio': dominio}) }}" class="btn-g fw-bold">NUEVO POST</a>
            </div>
            <div class="col-3 filter-user d-flex align-items-center justify-content-center">
                <img src="{{ asset('images/filter.svg') }}" alt="Filtro" class="icon-sntiasg me-2">
                <input type="text" id="filterTitle" class="form-control w-auto" placeholder="Buscar por título...">
            </div>
        </div>
    </div>


    <div class="container py-4">
        <div class="table-container table-responsive px-4">
            <table class="styled-table table-striped table-bordered text-center">
                <thead class="text-dark">
                <tr>
                    <th>Empresa</th>
                    <th>Título</th>
                    <th>Plataforma</th>
                    <th>URL</th>
                    <th>Fecha inicio</th>
                    <th>Fecha fin</th>
                    <th class="text-center">Acciones</th>
                </tr>
                </thead>
                <tbody>
                {% for social_media in social_medias %}
                    <tr>
                        <td>{{ social_media.company.name }}</td>
                        <td>{{ social_media.title }}</td>
                        <td>
                            <span class="badge bg-info">{{ social_media.platform }}</span>
                        </td>
                        <td>
                            <a href="{{ social_media.url }}" class="social-url" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>{{ social_media.url }}
                            </a>
                        </td>
                        <td>{{ social_media.startDate|date('d/m/Y H:i') }}</td>
                        <td>{{ social_media.endDate|date('d/m/Y H:i') }}</td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ path('app_social_media_show', {'id': social_media.id, 'dominio': dominio}) }}"
                                   class="btn btn-info btn-sm action-btn"
                                   title="Ver detalles">
                                    <i class="fas fa-eye me-1"></i>Ver
                                </a>
                                <a href="{{ path('app_social_media_edit', {'id': social_media.id, 'dominio': dominio}) }}"
                                   class="btn btn-warning btn-sm action-btn"
                                   title="Editar">
                                    <i class="fas fa-edit me-1"></i>Editar
                                </a>
                                <button type="button"
                                        class="btn btn-danger btn-sm action-btn"
                                        onclick="confirmDelete({{ social_media.id }}, '{{ social_media.title|e('js') }}')"
                                        title="Eliminar">
                                    <i class="fas fa-trash me-1"></i>Eliminar
                                </button>
                            </div>
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>No hay redes sociales registradas
                            </div>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>

            <p id="results-count" class="mt-3 text-white text-end fw-light">
                MOSTRANDO {{ social_medias|length }} REGISTROS.
            </p>
            <p id="no-results" class="mt-2 text-white fw-bold" style="display: none;">
                NO SE ENCONTRARON RESULTADOS.
            </p>
        </div>
    </div>

    <!-- Formularios ocultos para eliminación -->
    {% for social_media in social_medias %}
        <form id="deleteForm{{ social_media.id }}" method="post" action="{{ path('app_social_media_delete', {'id': social_media.id, 'dominio': dominio}) }}" style="display: none;">
            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ social_media.id) }}">
        </form>
    {% endfor %}

    <script>
        function confirmDelete(id, title) {
            if (confirm(`¿Estás seguro de que deseas eliminar el post "${title}"? Esta acción no se puede deshacer.`)) {
                document.getElementById('deleteForm' + id).submit();
            }
        }

        // Filtro de búsqueda existente
        document.addEventListener('DOMContentLoaded', function() {
            const filterInput = document.getElementById('filterTitle');
            const table = document.querySelector('.styled-table tbody');
            const rows = table.querySelectorAll('tr');
            const resultsCount = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            filterInput.addEventListener('input', function() {
                const filterValue = this.value.toLowerCase();
                let visibleRows = 0;

                rows.forEach(row => {
                    const titleCell = row.querySelector('td:nth-child(2)');
                    if (titleCell) {
                        const titleText = titleCell.textContent.toLowerCase();
                        if (titleText.includes(filterValue)) {
                            row.style.display = '';
                            visibleRows++;
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });

                // Actualizar contador de resultados
                if (visibleRows === 0 && filterValue !== '') {
                    resultsCount.style.display = 'none';
                    noResults.style.display = 'block';
                } else {
                    resultsCount.textContent = `MOSTRANDO ${visibleRows} REGISTROS.`;
                    resultsCount.style.display = 'block';
                    noResults.style.display = 'none';
                }
            });
        });
    </script>
{% endblock %}