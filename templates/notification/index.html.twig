{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Notificaciones{% endblock %}

{% block body %}
    <section class="header-sntiasg-r">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">NOTIFICACIONES</h1>
        </div>
    </section>
        
    <section class="my-5">
        <div class="container-fluid container-header">
            <div class="row btns-movil">
                <div class="col-6 btn-nt d-flex align-items-center">
                    <a href="{{ path('app_notification_new', {'dominio': dominio}) }}" class="btn-y">CREAR NOTIFICACIÓN</a>
                </div>
                
                {% set seen_titles = [] %}

                <div class="col-6 filter-nt mb-filter d-flex align-items-center">
                    <img src="{{ asset('images/filter.svg') }}" alt="Logo" class="icon-sntiasg">
                    <select id="filterTitle" class="form-select sntiasg-select" onchange="filterNotifications()">
                        <option value="all">PREDETERMINADO...</option>
                        {% for notification in notifications %}
                            {% if notification.title not in seen_titles %}
                                <option value="{{ notification.title }}">{{ notification.title }}</option>
                                {% set seen_titles = seen_titles|merge([notification.title]) %}
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
    </section>

    <section>
        <div class="container" id="notificationList">
            {% for notification in notifications %}
                <div class="row align-items-center mb-3 notification-item" data-title="{{ notification.title }}">
                    <div class="col-md-8 notification-container">
                        <div class="title-sntiasg title-notification">{{ notification.title }}</div>
                    </div>
                    <div class="col-md-4 d-flex gap-2 h-btn">
                        <a href="{{ path('app_notification_show', {'id': notification.id, 'dominio': dominio}) }}" class="btn-gr">VER</a>
                        <a href="{{ path('app_notification_edit', {'id': notification.id, 'dominio': dominio}) }}" class="btn-bl">EDITAR</a>

                        <button type="button" class="btn-red" data-bs-toggle="modal" data-bs-target="#modalDeleteNotification" data-form-id="delete-form-{{ notification.id }}">ELIMINAR</button>

                        <form id="delete-form-{{ notification.id }}" method="post" action="{{ path('app_notification_delete', {'id': notification.id, 'dominio': dominio}) }}">
                            <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ notification.id) }}">
                        </form>
                    </div>
                </div>
            {% endfor %}
        </div>
    </section>

    <div class="modal fade" id="modalDeleteNotification" tabindex="-1" aria-labelledby="modalDeleteNotificationLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content text-center p-3">
                <div class="modal-body modal-delete">
                    <h5 class="modal-title-sntiasg" id="modalDeleteNotificationLabel">¿QUIERES ELIMINAR ESTA NOTIFICACIÓN?</h5>
                    <div class="d-flex justify-content-center gap-2 mt-3">
                        <button id="btnConfirmDelete" type="button" class="btn-w">ELIMINAR</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        function filterNotifications() {
            const selected = document.getElementById('filterTitle').value;
            const items = document.querySelectorAll('.notification-item');

            items.forEach(item => {
                const title = item.getAttribute('data-title');
                if (selected === 'all' || title === selected) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
            let formToSubmit = null;

            document.querySelectorAll('.btn-red').forEach(button => {
                button.addEventListener('click', function () {
                    const formId = button.getAttribute('data-form-id');
                    formToSubmit = document.getElementById(formId);
                });
            });

            const confirmBtn = document.getElementById('btnConfirmDelete');
            if (confirmBtn) {
                confirmBtn.addEventListener('click', function () {
                    if (formToSubmit) {
                        formToSubmit.submit();
                        formToSubmit = null;
                    }
                });
            }
        });
    </script>
{% endblock %}
