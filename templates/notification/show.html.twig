{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Detalle de la Notificación{% endblock %}

{% block body %}
    <div class="d-flex align-items-center justify-content-center min-vh-100">
        <div class="container modal-r text-center container-show position-relative">

            <a href="{{ path('app_notification_index', {'dominio': dominio}) }}" class="btn-regresar-icon position-absolute top-0 end-0 m-3">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path id="Vector" d="M17.9999 17.9999L10.5 10.5M10.5 10.5L3 3M10.5 10.5L18 3M10.5 10.5L3 18" stroke="white" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
           
            <div class="row mb-3">
                <div class="col-md-12 modal-title-sntiasg">{{ notification.title }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12 modal-text-sntiasg"><strong class="fw-bold">Descripción: </strong>{{ notification.message }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12 modal-text-sntiasg"><strong class="fw-bold">Empresa(s): </strong>{{ notification.companyNames }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6 modal-text-sntiasg mb-movil"><strong class="fw-bold">Creado:</strong> {{ notification.createdAt ? notification.createdAt|date('d-m-Y H:i:s') : 'No disponible' }}</div>
                <div class="col-md-6 modal-text-sntiasg"><strong class="fw-bold">Actualizado:</strong> {{ notification.updatedAt ? notification.updatedAt|date('d-m-Y H:i:s') : 'No disponible' }}</div>
            </div>
        
            <div class="row mt-4 align-items-center">
                <div class="col-md-6 d-flex justify-content-center align-items-center">
                    <a href="{{ path('app_notification_edit', {'id': notification.id, 'dominio': dominio}) }}" class="btn-b">EDITAR</a>
                </div>
                <div class="col-md-6 d-flex justify-content-center align-items-center">
                    {{ include('notification/_delete_form.html.twig', {'button_label': 'ELIMINAR'}) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}