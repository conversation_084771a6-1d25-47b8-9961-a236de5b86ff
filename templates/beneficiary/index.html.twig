{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Beneficiarios{% endblock %}

{% block body %}
    <h1>Beneficiary index</h1>

    <table class="table">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Last_name</th>
                <th>Kinship</th>
                <th>Birthday</th>
                <th>Status</th>
                <th>Gender</th>
                <th>Education</th>
                <th>Photo</th>
                <th>Created_at</th>
                <th>Updated_at</th>
                <th>Curp</th>
                <th>actions</th>
            </tr>
        </thead>
        <tbody>
        {% for beneficiary in beneficiaries %}
            <tr>
                <td>{{ beneficiary.id }}</td>
                <td>{{ beneficiary.name }}</td>
                <td>{{ beneficiary.lastName }}</td>
                <td>{{ beneficiary.kinship }}</td>
                <td>{{ beneficiary.birthday ? beneficiary.birthday|date('d-m-Y H:i:s') : '' }}</td>
               <td>{{ beneficiary.status.value }}</td>

                <td>{{ beneficiary.gender }}</td>
                <td>{{ beneficiary.education }}</td>
                <td>{{ beneficiary.photo }}</td>
                <td>{{ beneficiary.createdAt ? beneficiary.createdAt|date('d-m-Y H:i:s') : '' }}</td>
                <td>{{ beneficiary.updatedAt ? beneficiary.updatedAt|date('d-m-Y H:i:s') : '' }}</td>
                <td>{{ beneficiary.curp }}</td>
                <td>
                    <a href="{{ path('app_beneficiary_show', {'id': beneficiary.id, 'dominio': dominio}) }}">show</a>
                    <a href="{{ path('app_beneficiary_edit', {'id': beneficiary.id, 'dominio': dominio}) }}">edit</a>
                </td>
            </tr>
        {% else %}
            <tr>
                <td colspan="13">no records found</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <a href="{{ path('app_beneficiary_new', {'dominio': dominio}) }}">Create new</a>
{% endblock %}
