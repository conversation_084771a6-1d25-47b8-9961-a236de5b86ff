{% extends 'base.html.twig' %}

{% block title %}Beneficiary{% endblock %}

{% block body %}
    <h1>Beneficiary</h1>

    <table class="table">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ beneficiary.id }}</td>
            </tr>
            <tr>
                <th>Name</th>
                <td>{{ beneficiary.name }}</td>
            </tr>
            <tr>
                <th>Last_name</th>
                <td>{{ beneficiary.lastName }}</td>
            </tr>
            <tr>
                <th>Kinship</th>
                <td>{{ beneficiary.kinship }}</td>
            </tr>
            <tr>
                <th>Birthday</th>
                <td>{{ beneficiary.birthday ? beneficiary.birthday|date('d-m-Y H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>Status</th>
               <td>{{ beneficiary.status.value }}</td>

            </tr>
            <tr>
                <th>Gender</th>
                <td>{{ beneficiary.gender }}</td>
            </tr>
            <tr>
                <th>Education</th>
                <td>{{ beneficiary.education }}</td>
            </tr>
            <tr>
                <th>Photo</th>
                <td>{{ beneficiary.photo }}</td>
            </tr>
            <tr>
                <th>Created_at</th>
                <td>{{ beneficiary.createdAt ? beneficiary.createdAt|date('d-m-Y H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>Updated_at</th>
                <td>{{ beneficiary.updatedAt ? beneficiary.updatedAt|date('d-m-Y H:i:s') : '' }}</td>
            </tr>
            <tr>
                <th>Curp</th>
                <td>{{ beneficiary.curp }}</td>
            </tr>
        </tbody>
    </table>

    <a href="{{ path('app_beneficiary_index') }}">back to list</a>

    <a href="{{ path('app_beneficiary_edit', {'id': beneficiary.id}) }}">edit</a>

    {{ include('beneficiary/_delete_form.html.twig') }}
{% endblock %}
