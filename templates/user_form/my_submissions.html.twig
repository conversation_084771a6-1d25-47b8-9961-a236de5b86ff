{% extends 'base.html.twig' %}

{% block title %}My Form Submissions{% endblock %}

{% block body %}
<div class="container mt-4">
    <h1 class="mb-4">My Form Submissions</h1>
    
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            {{ message }}
        </div>
    {% endfor %}
    
    {% if submissions|length > 0 %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Form</th>
                        <th>Submission Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for submission in submissions %}
                        <tr>
                            <td>{{ submission.formTemplate.name }}</td>
                            <td>{{ submission.createdAt|date('Y-m-d H:i') }}</td>
                            <td>
                                {% if submission.status.value == 'A' %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ path('app_user_forms_my_submission_show', {'id': submission.id, 'dominio': dominio}) }}" 
                                   class="btn btn-sm btn-primary">
                                    View Details
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            You haven't submitted any forms yet.
        </div>
    {% endif %}
    
    <div class="mt-4">
        <a href="{{ path('app_user_forms_index', {'dominio': dominio}) }}" class="btn btn-secondary">Back to Forms</a>
    </div>
</div>
{% endblock %}