{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Agregar Campos al Formulario{% endblock %}

{% block body %}
    <section class="header-sntiasg-b text-center">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">AGREGAR CAMPOS AL FORMULARIO</h1>
        </div>
    </section>

    <div class="container my-4">
        <div class="d-flex justify-content-end mb-3">
            <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-secondary fw-bold">
                <i class="fas fa-arrow-left me-1"></i> REGRESAR
            </a>
        </div>
        <div class="card shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i> Nuevo Campo</h5>
            </div>
            <div class="card-body">
                <!-- Flash messages are handled by SweetAlert2 in base template -->

                {% if errors is defined and errors %}
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            SwalHelper.validationErrors({{ errors|json_encode|raw }});
                        });
                    </script>
                {% endif %}

                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="label">Etiqueta</label>
                                <input type="text" id="label" name="label" class="form-control {% if errors.label is defined %}is-invalid{% endif %}"
                                       value="{{ app.request.request.get('label') }}" required>
                                <small class="form-text text-muted">Etiqueta que verán los usuarios</small>
                                {% if errors.label is defined %}
                                    <div class="invalid-feedback">{{ errors.label }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="name">Nombre</label>
                                <input type="text" id="name" name="name" class="form-control {% if errors.name is defined %}is-invalid{% endif %}"
                                       value="{{ app.request.request.get('name') }}" required>
                                <small class="form-text text-muted">Nombre del campo (usar en código, sin espacios)</small>
                                {% if errors.name is defined %}
                                    <div class="invalid-feedback">{{ errors.name }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="type">Tipo de Campo</label>
                                <select id="type" name="type" class="form-control {% if errors.type is defined %}is-invalid{% endif %}" required>
                                    <option value="">Seleccionar tipo...</option>
                                    <option value="text" {% if app.request.request.get('type') == 'text' %}selected{% endif %}>Texto</option>
                                    <option value="number" {% if app.request.request.get('type') == 'number' %}selected{% endif %}>Número</option>
                                    <option value="textarea" {% if app.request.request.get('type') == 'textarea' %}selected{% endif %}>Caja de Texto</option>
                                    <option value="select" {% if app.request.request.get('type') == 'select' %}selected{% endif %}>Selección</option>
                                    <option value="checkbox" {% if app.request.request.get('type') == 'checkbox' %}selected{% endif %}>Checkbox</option>
                                    <option value="radio" {% if app.request.request.get('type') == 'radio' %}selected{% endif %}>Radio Botones</option>
                                    <option value="date" {% if app.request.request.get('type') == 'date' %}selected{% endif %}>Fecha</option>
                                    <option value="file" {% if app.request.request.get('type') == 'file' %}selected{% endif %}>Subir Archivo</option>
                                </select>
                                {% if errors.type is defined %}
                                    <div class="invalid-feedback">{{ errors.type }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group mt-3">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="required" name="required">
                                    <label class="custom-control-label" for="required">Campo obligatorio</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="help">Texto de ayuda</label>
                                <input type="text" id="help" name="help" class="form-control">
                                <small class="form-text text-muted">Texto explicativo que aparece debajo del campo</small>
                            </div>
                            <div class="form-group">
                                <label for="options">Opciones</label>
                                <textarea id="options" name="options" class="form-control" rows="3"></textarea>
                                <small class="form-text text-muted">Para campos de tipo select, checkbox o radio. Ingresa las opciones separadas por comas.</small>
                            </div>
                            <div class="form-group mt-3">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="multiple" name="multiple">
                                    <label class="custom-control-label" for="multiple">Permitir selección múltiple</label>
                                    <small class="form-text text-muted">Para campos de tipo select o archivo</small>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cols">Ancho de columna</label>
                                <select id="cols" name="cols" class="form-control">
                                    <option value="">Por defecto</option>
                                    <option value="col-md-6">Mitad (50%)</option>
                                    <option value="col-md-4">Un tercio (33%)</option>
                                    <option value="col-md-3">Un cuarto (25%)</option>
                                    <option value="col-md-12">Ancho completo (100%)</option>
                                </select>
                                <small class="form-text text-muted">Ancho del campo en la distribución del formulario</small>
                            </div>
                            <div class="form-group">
                                <label for="textarea_cols">Filas del textarea</label>
                                <input type="number" id="textarea_cols" name="textarea_cols" class="form-control" min="1" max="20" value="3">
                                <small class="form-text text-muted">Cantidad de filas visibles para campos de texto largo</small>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mt-4 d-flex justify-content-end gap-2">
                        <button type="submit" class="btn btn-primary fw-bold">Agregar campo</button>
                        <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-secondary fw-bold">Cancelar</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const typeSelect = document.getElementById('type');
            const optionsGroup = document.getElementById('options').closest('.form-group');
            const multipleGroup = document.getElementById('multiple').closest('.form-group');
            const textareaColsGroup = document.getElementById('textarea_cols').closest('.form-group');
            function updateFieldVisibility() {
                const selectedType = typeSelect.value;
                if (selectedType === 'select' || selectedType === 'checkbox' || selectedType === 'radio') {
                    optionsGroup.style.display = 'block';
                } else {
                    optionsGroup.style.display = 'none';
                }
                if (selectedType === 'select' || selectedType === 'file') {
                    multipleGroup.style.display = 'block';
                } else {
                    multipleGroup.style.display = 'none';
                }
                if (selectedType === 'textarea') {
                    textareaColsGroup.style.display = 'block';
                } else {
                    textareaColsGroup.style.display = 'none';
                }
            }
            updateFieldVisibility();
            typeSelect.addEventListener('change', updateFieldVisibility);
        });
    </script>
{% endblock %}