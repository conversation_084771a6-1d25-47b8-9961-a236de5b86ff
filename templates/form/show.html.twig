{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Gestionar Formulario: {{ form_template.name }}{% endblock %}

{% block body %}
    <section class="header-sntiasg-b py-3">
        <div class="container text-center">
            <h1 class="title-sntiasg mb-0">Gestionar Formulario: {{ form_template.name }}</h1>
            <p class="text-light">Visualiza, organiza y agrega campos al formulario de manera sencilla.</p>
        </div>
    </section>

    <div class="container my-4">
        <!-- Flash messages are handled by SweetAlert2 in base template -->

        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> Detalles del Formulario</h4>
                    </div>
                    <div class="card-body p-0">
                        <table class="table table-bordered table-striped mb-0">
                            <tr><th>ID</th><td>{{ form_template.id }}</td></tr>
                            <tr><th>Nombre</th><td>{{ form_template.name }}</td></tr>
                            <tr><th>Descripción</th><td>{{ form_template.description }}</td></tr>
                            <tr><th>Creado</th><td>{{ form_template.createdAt|date('d/m/Y H:i') }}</td></tr>
                            <tr><th>Actualizado</th><td>{{ form_template.updatedAt|date('d/m/Y H:i') }}</td></tr>
                            <tr><th>Estado</th><td>{% if form_template.status.value == '1' %}<span class="badge bg-success">Activo</span>{% else %}<span class="badge bg-danger">Inactivo</span>{% endif %}</td></tr>
                            <tr>
                                <th>Empresas Autorizadas</th>
                                <td>
                                    {% if form_template.isAvailableForAllCompanies %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-globe me-1"></i> Todas las empresas
                                        </span>
                                    {% else %}
                                        {% for company in form_template.companies %}
                                            <span class="badge bg-dark text-white me-1">
                                                <i class="fas fa-building me-1"></i>{{ company.name }}
                                            </span>
                                        {% endfor %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr><th>Número de campos activos</th><td>{{ form_template.formTemplateFields|length }}</td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-eye me-2"></i> Vista Previa del Formulario</h4>
                    </div>
                    <div class="card-body">
                        {% if form_template.formTemplateFields|length > 0 %}
                            <form>
                                {% for field in form_template.formTemplateFields %}
                                    {% if field.status.value == '1' %}
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">{{ field.label }} {% if field.isRequired %}<span class="text-danger">*</span>{% endif %}</label>

                                            {% if field.type == 'text' %}
                                                <input type="text" class="form-control" placeholder="{{ field.help }}" disabled>
                                            {% elseif field.type == 'textarea' %}
                                                <textarea class="form-control" rows="3" placeholder="{{ field.help }}" disabled></textarea>
                                            {% elseif field.type == 'select' %}
                                                <select class="form-select" disabled>
                                                    <option>Seleccione una opción</option>
                                                </select>
                                            {% elseif field.type == 'date' %}
                                                <input type="date" class="form-control" disabled>
                                            {% elseif field.type == 'file' %}
                                                <input type="file" class="form-control" disabled>
                                            {% elseif field.type == 'checkbox' %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" disabled>
                                                    <label class="form-check-label">{{ field.help|default('Opción') }}</label>
                                                </div>
                                            {% elseif field.type == 'radio' %}
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" disabled>
                                                    <label class="form-check-label">{{ field.help|default('Opción') }}</label>
                                                </div>
                                            {% endif %}

                                            {% if field.help %}
                                                <div class="form-text">{{ field.help }}</div>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                {% endfor %}
                                <button class="btn btn-primary w-100" disabled>
                                    <i class="fas fa-paper-plane me-1"></i> Enviar Formulario (Vista Previa)
                                </button>
                            </form>
                        {% else %}
                            <p class="text-center text-muted">No hay campos aún en este formulario.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-md-12 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-users me-2"></i> Información de Acceso</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-building me-2"></i> Empresas Autorizadas</h6>
                                {% if form_template.isAvailableForAllCompanies %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-globe me-2"></i>
                                        <strong>Acceso Universal:</strong> Este formulario está disponible para todas las empresas del sistema.
                                    </div>
                                {% else %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-lock me-2"></i>
                                        <strong>Acceso Restringido:</strong> Solo las siguientes empresas pueden acceder a este formulario:
                                    </div>
                                    <div class="list-group">
                                        {% for company in form_template.companies %}
                                            <div class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-building text-primary me-2"></i>
                                                <strong class="text-dark">{{ company.name }}</strong>
                                                <span class="badge bg-success ms-auto">Activa</span>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-chart-bar me-2"></i> Estadísticas de Acceso</h6>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5 class="text-primary">{{ form_template.companies|length }}</h5>
                                                <small class="text-muted">Empresas Específicas</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5 class="text-success">{{ form_template.formEntries|length }}</h5>
                                                <small class="text-muted">Total Respuestas</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1 text-white"></i> Modificar Accesos
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de Acciones y Exportación
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0"><i class="fas fa-tools me-2"></i> Acciones del Formulario</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-edit me-2"></i> Gestión</h6>
                        <div class="btn-group-vertical d-grid gap-2">
                            <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i> Editar Formulario
                            </a>
                            <a href="{{ path('app_forms_fields_new', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-success">
                                <i class="fas fa-plus me-2"></i> Agregar Campo
                            </a>
                            <a href="{{ path('app_forms_submissions', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-info">
                                <i class="fas fa-inbox me-2"></i> Ver Envíos
                            </a>
                            <a href="{{ path('app_forms_preview', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-warning" target="_blank">
                                <i class="fas fa-eye me-2"></i> Vista Previa
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-download me-2"></i> Exportación</h6>
                        <div class="btn-group-vertical d-grid gap-2">
                            <a href="{{ path('app_forms_export_json', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-file-code me-2"></i> Exportar como JSON
                            </a>
                            <a href="{{ path('app_forms_export_excel', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-outline-success">
                                <i class="fas fa-file-excel me-2"></i> Exportar como Excel
                            </a>
                            <button type="button" class="btn btn-outline-warning" onclick="duplicateForm()">
                                <i class="fas fa-copy me-2"></i> Duplicar Formulario
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
-->
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0"><i class="fas fa-list-alt me-2"></i> Campos del Formulario</h4>
                <a href="{{ path('app_forms_fields_new', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i> Agregar Campo
                </a>
            </div>
            <div class="card-body p-0">
                {% if form_template.formTemplateFields|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover align-middle mb-0">
                            <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>Etiqueta</th>
                                <th>Tipo</th>
                                <th>Obligatorio</th>
                                <th>Acciones</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for field in form_template.formTemplateFields %}
                                {% if field.status.value == '1' %}
                                    <tr>
                                        <td>{{ field.id }}</td>
                                        <td>{{ field.label }}</td>
                                        <td>{{ field.type|capitalize }}</td>
                                        <td>
                                            {% if field.isRequired %}
                                                <span class="badge bg-success">Sí</span>
                                            {% else %}
                                                <span class="badge bg-secondary">No</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ path('app_forms_fields_edit', {'id': form_template.id, 'fieldId': field.id, 'dominio': dominio}) }}" class="btn btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="post" action="{{ path('app_forms_fields_delete', {'id': form_template.id, 'fieldId': field.id, 'dominio': dominio}) }}" onsubmit="return confirmDeleteField(event, '{{ field.label }}');">
                                                    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ field.id) }}">
                                                    <button class="btn btn-danger"><i class="fas fa-trash"></i></button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="p-4 text-center text-muted">
                        No hay campos en este formulario todavía. Utiliza el botón "Agregar Campo" para empezar.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        function duplicateForm() {
            const formName = '{{ form_template.name }}';

            SwalHelper.prompt({
                title: 'Duplicar formulario',
                text: 'Ingrese el nombre para el formulario duplicado:',
                inputValue: formName + ' (Copia)',
                inputPlaceholder: 'Nombre del formulario...'
            }).then((result) => {
                if (result.isConfirmed && result.value.trim() !== '') {
                    // TODO: Implementar endpoint para duplicar formulario
                    showInfo('Funcionalidad de duplicación será implementada próximamente.');
                }
            });
        }

        function confirmDeleteField(event, fieldName) {
            event.preventDefault();

            confirmDelete(`el campo "${fieldName}"`).then((result) => {
                if (result.isConfirmed) {
                    event.target.submit();
                }
            });

            return false;
        }
    </script>

    <style>
        /* Hover del botón Modificar Accesos: azul claro a azul fuerte */
        .btn-primary:hover {
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
            color: white !important;
            transform: none !important;
        }

        .btn-primary:hover i {
            color: white !important;
        }
    </style>
{% endblock %}
