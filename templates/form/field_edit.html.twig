{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Editar Campo: {{ field.label }}{% endblock %}

{% block body %}
    <section class="header-sntiasg-b py-3 text-white">
        <div class="container text-center">
            <h1 class="h4 mb-0"><i class="fas fa-edit me-2"></i> Editar Campo: {{ field.label }}</h1>
        </div>
    </section>

    <div class="container my-4">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>{{ field.label }}</h4>
            </div>
            <div class="card-body">
                <!-- Flash messages are handled by SweetAlert2 in base template -->

                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="label" class="form-label">Eti<PERSON><PERSON> del Campo</label>
                                <input type="text" id="label" name="label" class="form-control" value="{{ field.label }}" required>
                                <small class="form-text text-muted">Texto que se muestra al usuario.</small>
                            </div>

                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre Interno</label>
                                <input type="text" id="name" name="name" class="form-control" value="{{ field.name }}" required>
                                <small class="form-text text-muted">Usado en el sistema, sin espacios.</small>
                            </div>

                            <div class="mb-3">
                                <label for="type" class="form-label">Tipo de Campo</label>
                                <select id="type" name="type" class="form-select" required>
                                    <option value="text" {% if field.type == 'text' %}selected{% endif %}>Texto</option>
                                    <option value="textarea" {% if field.type == 'textarea' %}selected{% endif %}>Área de Texto</option>
                                    <option value="select" {% if field.type == 'select' %}selected{% endif %}>Lista Desplegable</option>
                                    <option value="checkbox" {% if field.type == 'checkbox' %}selected{% endif %}>Checkbox</option>
                                    <option value="radio" {% if field.type == 'radio' %}selected{% endif %}>Radio</option>
                                    <option value="date" {% if field.type == 'date' %}selected{% endif %}>Fecha</option>
                                    <option value="file" {% if field.type == 'file' %}selected{% endif %}>Archivo</option>
                                </select>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="required" name="required" {% if field.isRequired %}checked{% endif %}>
                                <label class="form-check-label text-dark" for="required">Campo Obligatorio</label>
                            </div>

                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="help" class="form-label">Texto de Ayuda</label>
                                <input type="text" id="help" name="help" class="form-control" value="{{ field.help }}">
                                <small class="form-text text-muted">Texto explicativo debajo del campo.</small>
                            </div>

                            <div class="mb-3">
                                <label for="options" class="form-label">Opciones</label>
                                <textarea id="options" name="options" class="form-control" rows="3">{{ field.options }}</textarea>
                                <small class="form-text text-muted">Separadas por comas para listas, radios o checkboxes.</small>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="multiple" name="multiple" {% if field.isMultiple %}checked{% endif %}>
                                <label class="form-check-label" for="multiple">Permitir selección múltiple</label>
                                <small class="form-text text-muted">Solo para listas o archivos.</small>
                            </div>

                            <div class="mb-3">
                                <label for="cols" class="form-label">Ancho en Pantalla</label>
                                <select id="cols" name="cols" class="form-select">
                                    <option value="" {% if field.cols is empty %}selected{% endif %}>Predeterminado</option>
                                    <option value="col-md-6" {% if field.cols == 'col-md-6' %}selected{% endif %}>Media Pantalla</option>
                                    <option value="col-md-4" {% if field.cols == 'col-md-4' %}selected{% endif %}>Un Tercio</option>
                                    <option value="col-md-3" {% if field.cols == 'col-md-3' %}selected{% endif %}>Un Cuarto</option>
                                    <option value="col-md-12" {% if field.cols == 'col-md-12' %}selected{% endif %}>Completo</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="textarea_cols" class="form-label">Filas para Área de Texto</label>
                                <input type="number" id="textarea_cols" name="textarea_cols" class="form-control" min="1" max="20" value="{{ field.textareaCols|default(3) }}">
                                <small class="form-text text-muted">Solo para campos de tipo área de texto.</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Guardar Cambios
                        </button>
                        <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> Cancelar
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const typeSelect = document.getElementById('type');
            const optionsGroup = document.getElementById('options').closest('.mb-3');
            const multipleGroup = document.getElementById('multiple').closest('.mb-3');
            const textareaColsGroup = document.getElementById('textarea_cols').closest('.mb-3');

            function updateVisibility() {
                const selectedType = typeSelect.value;
                optionsGroup.style.display = ['select', 'checkbox', 'radio'].includes(selectedType) ? 'block' : 'none';
                multipleGroup.style.display = ['select', 'file'].includes(selectedType) ? 'block' : 'none';
                textareaColsGroup.style.display = selectedType === 'textarea' ? 'block' : 'none';
            }

            updateVisibility();
            typeSelect.addEventListener('change', updateVisibility);
        });
    </script>
{% endblock %}
