{% extends 'base.html.twig' %}
{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Form Templates{% endblock %}

{% block stylesheets %}
    {{ parent() }}

{% endblock %}

{% block body %}

    <section class="header-sntiasg-b py-3">
        <div class="container text-center">
            <h1 class="h2 mb-0 font-weight-bold"><i class="fas fa-file-alt mr-2"></i> Plantillas de Formularios</h1>
            <br>
            <a href="{{ path('app_forms_new', {'dominio': dominio}) }}" class="btn btn-success btn-sm shadow-sm">
                <i class="fas fa-plus-circle mr-1"></i> Nuevo Formulario
            </a>
        </div>
    </section>

    <div class="container text-center">
        <div class="row">
            <div class="col-12 d-flex justify-content-between my-3">
                <div class="col-3 new-user">
                    <a href="{{ path('app_forms_new', {'dominio': dominio}) }}" class="btn-g fw-bold">DAR DE ALTA</a>
                </div>
                <div class="col-3">
                    <button type="button" class="btn-o text-white fw-bold mb-2" onclick="exportSelected()" id="exportBtn" disabled>
                        EXPORTAR SELECCIONADOS
                    </button>
                </div>
            </div>
        </div>

        <div class="table-container table-responsive px-4">
            {% if form_templates is empty %}
                <div class="alert alert-info m-3">
                    <i class="fas fa-info-circle mr-2"></i> No hay formularios creados aún.
                    <a href="{{ path('app_forms_new', {'dominio': dominio}) }}" class="alert-link">
                        Crear el primer formulario
                    </a>
                </div>
            {% else %}
                <form id="exportForm" action="{{ path('app_forms_export_multiple', {'dominio': dominio}) }}" method="POST">
                    <table class="styled-table table-striped table-bordered text-center">
                        <thead class="table-primary text-dark">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleAll()">
                            </th>
                            <th>ID</th>
                            <th>NOMBRE</th>
                            <th>DESCRIPCIÓN</th>
                            <th>CAMPOS</th>
                            <th>FECHA DE CREACIÓN</th>
                            <th>ACCIONES</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for form_template in form_templates %}
                            <tr>
                                <td>
                                    <input type="checkbox" name="form_ids[]" value="{{ form_template.id }}" class="form-checkbox" onchange="updateExportButton()">
                                </td>
                                <td>{{ form_template.id }}</td>
                                <td>{{ form_template.name }}</td>
                                <td>
                                    {{ form_template.description|slice(0, 70) }}{% if form_template.description|length > 70 %}...{% endif %}
                                </td>
                                <td>
                                    {{ form_template.fields_count }}
                                </td>
                                <td>{{ form_template.created_at|date('d/m/Y') }}</td>
                                <td>
                                    <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}" class="btn-v fw-bold">VER</a>
                                    <a href="{{ path('app_forms_edit', {'id': form_template.id, 'dominio': dominio}) }}" class="btn-e fw-bold">EDITAR</a>
                                    <button type="button" class="btn-r fw-bold" data-bs-toggle="modal" data-bs-target="#deleteModal{{ form_template.id }}">ELIMINAR</button>
                                </td>
                            </tr>

                                <!-- Modal de Eliminación -->
                                <div class="modal fade" id="deleteModal{{ form_template.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ form_template.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-delete">
                                        <div class="modal-content">
                                            <div class="modal-body">
                                                <h5 class="modal-title-sntiasg">ELIMINAR FORMULARIO</h5>
                                                <p class="modal-text-sntiasg">¿ESTÁ SEGURO DE ELIMINAR ESTE FORMULARIO?</p>
                                                <p class="modal-text-sntiasg">"{{ form_template.name }}" (ID: {{ form_template.id }})</p>
                                                <div class="d-flex justify-content-between mt-4">
                                                    <button type="button" class="btn-w" data-bs-dismiss="modal">CANCELAR</button>
                                                    <form action="{{ path('app_forms_delete', {'id': form_template.id, 'dominio': dominio}) }}" method="post">
                                                        <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ form_template.id) }}">
                                                        <button type="submit" class="btn-red">ELIMINAR</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <tr>
                                    <td colspan="7">SIN FORMULARIOS DISPONIBLES</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                        </form>

                        <p id="results-count" class="mt-3 text-white text-end fw-light">
                            MOSTRANDO {{ form_templates|length }} FORMULARIOS
                        </p>
                        <p id="no-results" class="mt-2 text-white fw-bold" style="display: none;">
                            NO SE ENCONTRARON FORMULARIOS.
                        </p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const input = document.getElementById('filterTitle');
            const rows = document.querySelectorAll('tbody tr');
            const resultText = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            // Función para filtrar formularios
            function filterForms() {
                const searchTerm = input ? input.value.toLowerCase().trim() : '';
                let count = 0;

                rows.forEach(row => {
                    const visible = Array.from(row.cells).some(cell => 
                        cell.textContent.toLowerCase().includes(searchTerm)
                    );
                    row.style.display = visible ? '' : 'none';
                    if (visible) count++;
                });

                if (resultText) {
                    resultText.textContent = `MOSTRANDO ${count} FORMULARIO${count === 1 ? '' : 'S'}`;
                }
                if (noResults) {
                    noResults.style.display = count === 0 ? 'block' : 'none';
                }
            }

            // Agregar filtro de búsqueda
            if (input) {
                input.addEventListener('input', filterForms);
            }

        // Funciones para exportación múltiple
        function toggleAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.form-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateExportButton();
        }

        function updateExportButton() {
            const checkboxes = document.querySelectorAll('.form-checkbox:checked');
            const exportBtn = document.getElementById('exportBtn');

            exportBtn.disabled = checkboxes.length === 0;
            exportBtn.textContent = checkboxes.length > 0
                ? `Exportar ${checkboxes.length} Seleccionados`
                : 'Exportar Seleccionados';
        }

        function exportSelected() {
            const checkboxes = document.querySelectorAll('.form-checkbox:checked');

            if (checkboxes.length === 0) {
                showWarning('Debe seleccionar al menos un formulario para exportar.');
                return;
            }

            confirmAction({
                title: '¿Exportar formularios?',
                text: `Se exportarán ${checkboxes.length} formulario(s) en un archivo ZIP`,
                confirmButtonText: 'Sí, exportar'
            }).then((result) => {
                if (result.isConfirmed) {
                    showLoading('Preparando exportación...');
                    document.getElementById('exportForm').submit();
                }
            });
        }
    </script>
{% endblock %}
