{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Nuevo Formulario{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Estilos personalizados para Select2 de empresas */
        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: 45px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 5px;
            transition: all 0.3s ease;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection--multiple {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            margin: 2px;
            font-size: 0.875rem;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #ffcccc;
        }

        .select2-dropdown {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .companies-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
        }
    </style>
{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">Crear Nuevo Formulario</h1>
        </div>
    </section>

    <br>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crear Nuevo Formulario</h3>
                        <div class="card-tools">
                            <a href="{{ path('app_forms_index', {'dominio': app.request.attributes.get('dominio')}) }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> Regresar
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Flash messages are handled by SweetAlert2 in base template -->

                        {% if errors is defined and errors %}
                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    SwalHelper.validationErrors({{ errors|json_encode|raw }});
                                });
                            </script>
                        {% endif %}

                        <form method="post">
                            <div class="form-group">
                                <label for="name">Nombre del Formulario</label>
                                <input type="text" id="name" name="name" class="form-control {% if errors.name is defined %}is-invalid{% endif %}"
                                       value="{{ app.request.request.get('name') }}" required>
                                {% if errors.name is defined %}
                                    <div class="invalid-feedback">{{ errors.name }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                <label for="description">Descripción</label>
                                <textarea id="description" name="description" class="form-control {% if errors.description is defined %}is-invalid{% endif %}" rows="5">{{ app.request.request.get('description') }}</textarea>
                                <small class="form-text text-muted">Incluir una descripción para el formulario.</small>
                                {% if errors.description is defined %}
                                    <div class="invalid-feedback">{{ errors.description }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                <label for="companyIds" class="fw-bold">
                                    <i class="fas fa-building me-2 text-primary"></i>Empresas Autorizadas
                                </label>
                                <select id="companyIds" name="companyIds[]" class="form-select select2-multiple {% if errors.companyIds is defined %}is-invalid{% endif %}" multiple="multiple" data-placeholder="🌐 Seleccionar empresas (vacío = todas las empresas)">
                                    {% for company in companies %}
                                    {% set requestCompanyIds = app.request.request.get('companyIds') is iterable 
                                        ? app.request.request.get('companyIds') 
                                        : [app.request.request.get('companyIds')] %}
                                        {% set isSelected = false %}
                                        {% if requestCompanyIds %}
                                            {% if requestCompanyIds is iterable %}
                                                {% for companyId in requestCompanyIds %}
                                                    {% if companyId == company.id %}
                                                        {% set isSelected = true %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                {% if requestCompanyIds == company.id %}
                                                    {% set isSelected = true %}
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                        <option value="{{ company.id }}" {% if isSelected %}selected{% endif %}>
                                            🏢 {{ company.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="companies-info">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-info-circle text-primary me-2"></i>
                                            <small class="fw-bold text-dark">Configuración de Acceso:</small>
                                        </div>
                                        <div class="d-flex gap-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-globe text-info me-1"></i>
                                                <small class="text-dark"><strong>Vacío:</strong> Todas</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-lock text-warning me-1"></i>
                                                <small class="text-dark"><strong>Selección:</strong> Específicas</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% if errors.companyIds is defined %}
                                    <div class="invalid-feedback">{{ errors.companyIds }}</div>
                                {% endif %}
                            </div>
                            <br>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Crear</button>
                                <a href="{{ path('app_forms_index', {'dominio': app.request.attributes.get('dominio')}) }}" class="btn btn-secondary">Cancelar</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            // Inicializar Select2 para el selector de empresas con configuración mejorada
            $('#companyIds').select2({
                theme: 'bootstrap-5',
                placeholder: '🌐 Seleccionar empresas (vacío = todas las empresas)',
                allowClear: true,
                width: '100%',
                closeOnSelect: false,
                tags: false,
                tokenSeparators: [','],
                escapeMarkup: function (markup) {
                    return markup;
                },
                templateResult: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fas fa-building me-2"></i>' + option.text + '</span>');
                },
                templateSelection: function(option) {
                    if (!option.id) return option.text;
                    return $('<span><i class="fas fa-building me-1"></i>' + option.text + '</span>');
                }
            });

            // Agregar evento para mostrar información cuando se selecciona/deselecciona
            $('#companyIds').on('select2:select select2:unselect', function (e) {
                const selectedCount = $(this).val() ? $(this).val().length : 0;
                const totalCompanies = $(this).find('option').length;

                if (selectedCount === 0) {
                    console.log('✅ Formulario disponible para TODAS las empresas');
                } else {
                    console.log(`🔒 Formulario restringido a ${selectedCount} de ${totalCompanies} empresas`);
                }
            });
        });
    </script>
{% endblock %}