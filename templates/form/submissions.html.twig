{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Registros de la Presentación{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header text-center">
            <h1 class="title-sntiasg">REGISTROS DE LA PRESENTACIÓN</h1>
        </div>
    </section>

    <div class="container my-4">
        <div class="d-flex justify-content-end mb-3">
            <a href="{{ path('app_forms_show', {'id': form_template.id, 'dominio': dominio}) }}" class="btn btn-secondary fw-bold">
                <i class="fas fa-arrow-left me-1"></i> REGRESAR
            </a>
        </div>
        <div class="card shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-list me-2"></i> Registros para: {{ form_template.name }}</h3>
            </div>
            <div class="card-body">
                {% for message in app.flashes('success') %}
                    <div class="alert alert-success">
                        {{ message }}
                    </div>
                {% endfor %}

                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Usuario</th>
                            <th>Enviado</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for submission in submissions %}
                            <tr>
                                <td>{{ submission.id }}</td>
                                <td>
                                    {% if submission.user %}
                                        {{ submission.user.email }}
                                    {% else %}
                                        Anónimo
                                    {% endif %}
                                </td>
                                <td>{{ submission.createdAt|date('d/m/Y H:i') }}</td>
                                <td>
                                    {% if submission.status.value == 'A' %}
                                        <span class="badge bg-success">Activo</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactivo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ path('app_forms_submission_show', {'id': submission.id, 'dominio': dominio}) }}" class="btn btn-info btn-sm fw-bold">
                                        <i class="fas fa-eye"></i> Ver
                                    </a>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="5" class="text-center">No se encontraron registros</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}