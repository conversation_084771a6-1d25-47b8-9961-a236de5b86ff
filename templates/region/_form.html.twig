{{ form_start(form) }}
  <div class="row">
    <div class="col-md-12 col-movil margin-form-sntiasg">
      {{ form_label(form.name) }}
      {{ form_widget(form.name) }}

      {% for error in form.name.vars.errors %}
        <small class="text-danger-email my-2">
          {{ error.message }}
        </small>
      {% endfor %}
    </div>

  </div>

  <div class="d-flex justify-content-center col-12 text-center ">
    <button class="btn-y px-5">{{ button_label|default('CREAR') }}</button>
  </div>
{{ form_end(form) }}

{# Replace all path/url calls using dominio with app.request.attributes.get('dominio') #}
