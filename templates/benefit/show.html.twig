{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Detalle del Beneficio{% endblock %}

{% block body %}
    <div class="d-flex align-items-center justify-content-center min-vh-100">
        <div class="container modal-g text-center container-image-show position-relative">
            <a href="{{ path('app_benefit_index', {'dominio': dominio}) }}" class="btn-regresar-icon position-absolute top-0 end-0 m-3">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path id="Vector" d="M17.9999 17.9999L10.5 10.5M10.5 10.5L3 3M10.5 10.5L18 3M10.5 10.5L3 18" stroke="white" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>

            <div class="row mb-3">
                <div class="col-md-12 image-show">
                    {% if benefit.image %}
                        <div data-bs-toggle="modal" data-bs-target="#imageModal-{{ benefit.id }}">
                            <img src="{{ image_full_url(benefit.image) }}" alt="{{ benefit.title }}" class="img-detail object-fit-cover">
                        </div>

                        <div class="modal fade" id="imageModal-{{ benefit.id }}" tabindex="-1" aria-labelledby="imageModalLabel-{{ benefit.id }}" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg">
                                <div class="modal-content">
                                    <div class="modal-body p-0 position-relative">
                                        <img src="{{ image_full_url(benefit.image) }}" alt="Imagen completa" class="img-fluid w-100 rounded">
                                       
                                        <a href="{{ path('app_benefit_index', {'dominio': dominio}) }}" class="btn-regresar-icon position-absolute top-0 end-0 m-3">
                                            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path id="Vector" d="M17.9999 17.9999L10.5 10.5M10.5 10.5L3 3M10.5 10.5L18 3M10.5 10.5L3 18" stroke="black" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <p class="text-center text-light text-uppercase">No hay imagen disponible.</p>
                    {% endif %}
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12 modal-title-sntiasg">{{ benefit.title }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12 modal-text-sntiasg"><strong class="fw-bold">Descripción: </strong>{{ benefit.description }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12 modal-text-sntiasg"><strong class="fw-bold">Empresa(s): </strong>{{ benefit.companyNames }}</div>
            </div>
        
            <div class="row mb-3">
                <div class="col-md-6 modal-text-sntiasg mb-movil"><strong class="fw-bold">Inicio de vigencia: </strong> {{ benefit.validityStartDate ? benefit.validityStartDate|date('d-m-Y H:i:s') : 'Sin fecha' }}</div>
                <div class="col-md-6 modal-text-sntiasg"><strong class="fw-bold">Fin de vigencia: </strong>{{ benefit.validityEndDate ? benefit.validityEndDate|date('d-m-Y H:i:s') : 'Sin fecha' }}</div>
            </div>
        
            <div class="row mb-3">
                <div class="col-md-6 modal-text-sntiasg mb-movil"><strong class="fw-bold">Creado:</strong> {{ benefit.createdAt ? benefit.createdAt|date('d-m-Y H:i:s') : 'No disponible' }}</div>
                <div class="col-md-6 modal-text-sntiasg"><strong class="fw-bold">Actualizado:</strong> {{ benefit.updatedAt ? benefit.updatedAt|date('d-m-Y H:i:s') : 'No disponible' }}</div>
            </div>

            <div class="row mt-5 align-items-center">
                <div class="col-md-6 d-flex justify-content-center align-items-center">
                    <a href="{{ path('app_benefit_edit', {'id': benefit.id, 'dominio': dominio}) }}" class="btn-b m-0">EDITAR</a>
                </div>
                <div class="col-md-6 d-flex justify-content-center align-items-center">
                    {{ include('benefit/_delete_form.html.twig', {'button_label': 'ELIMINAR'}) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
