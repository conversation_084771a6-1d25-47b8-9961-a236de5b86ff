{% set dominio = app.request.attributes.get('dominio') %}

<button type="button" class="btn-delete-benefit" data-bs-toggle="modal" data-bs-target="#modalDeleteBenefit" data-form-id="delete-form-{{ benefit.id }}">ELIMINAR</button>

<form id="delete-form-{{ benefit.id }}" method="post" action="{{ path('app_benefit_delete', {'id': benefit.id, 'dominio': dominio}) }}">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ benefit.id) }}">
</form>

<div class="modal fade" id="modalDeleteBenefit" tabindex="-1" aria-labelledby="modalDeleteBenefitLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content text-center p-3">
            <div class="modal-body modal-delete">
                <h5 class="modal-title-sntiasg" id="modalDeleteBenefitLabel">¿Eliminar beneficio?</h5>
                <div class="d-flex justify-content-center gap-2 mt-3">
                    <button id="btnConfirmDelete" type="button" class="btn-w">ELIMINAR</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let formToSubmit = null;

        document.querySelectorAll('.btn-delete-benefit').forEach(button => {
            button.addEventListener('click', function () {
                const formId = button.getAttribute('data-form-id');
                formToSubmit = document.getElementById(formId);
            });
        });

        const confirmBtn = document.getElementById('btnConfirmDelete');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function () {
                if (formToSubmit) {
                    formToSubmit.submit();
                    formToSubmit = null;
                }
            });
        }
    });
</script>
