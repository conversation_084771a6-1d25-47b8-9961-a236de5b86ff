{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Beneficios{% endblock %}

{% block body %}
    <section class="header-sntiasg-o">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">BENEFICIOS</h1>
        </div>
    </section>

    <section class="d-flex justify-content-center m-5">
        <a href="{{ path('app_benefit_new', {'dominio': dominio}) }}" class="btn-y">AÑADIR BENEFICIO</a>
    </section>

    <section class="container my-5">
        <div class="row g-4 justify-content-center">
            {% for benefit in benefits %}
                <div class="col-md-4 col-sm-6 d-flex">
                    <a href="{{ path('app_benefit_show', {'id': benefit.id, 'dominio': dominio}) }}" class="text-decoration-none">
                        <div class="card h-100 benefit-card">
                            {% if benefit.image %}
                                <img src="{{ image_full_url(benefit.image) }}" alt="{{ benefit.title }}" class="card-img-top">
                            {% else %}
                                <p class="text-center text-light text-uppercase">No hay imagen disponible.</p>
                            {% endif %}
                            <div class="card-body d-flex flex-column justify-content-between">
                                <h5 class="card-title-sntiasg">{{ benefit.title }}</h5>
                            </div>
                        </div>
                    </a>
                </div>
            {% else %}
                <p class="text-center text-light text-uppercase">No hay beneficios disponibles.</p>
            {% endfor %}
        </div>
    </section>
    
{% endblock %}
