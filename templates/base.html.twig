<!DOCTYPE html>
<html>
{% set dominio = app.request.attributes.get('dominio') %}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}SNTIASG{% endblock %} | sntiasg.mx</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=League+Spartan&family=Manjari&family=Montserrat&display=swap" rel="stylesheet">

    {% block stylesheets %}
        {{ encore_entry_link_tags('app') }}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    {% endblock %}

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous"/>
    <!-- TEST: FontAwesome 5 fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" crossorigin="anonymous"/>
    <!-- TEST: FontAwesome 4 fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" crossorigin="anonymous"/>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script src="{{ asset('bundles/fosjsrouting/js/router.min.js') }}"></script>
    <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}"></script>

    {# htmx for chat #}
    <!-- HTMX for dynamic chat & interactions -->
    <script src="https://cdn.jsdelivr.net/npm/htmx.org@2.0.6/dist/htmx.min.js" integrity="sha384-Akqfrbj/HpNVo8k11SXBb6TlBWmXXlYQrCSqEWmyKJe+hDm3Z/B2WVG4smwBkRVm" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/htmx-ext-sse@2.2.2" crossorigin="anonymous"></script>


    {% block javascripts %}
        {{ encore_entry_script_tags('app') }}
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- SweetAlert2 Helper -->
        <script src="{{ asset('js/sweetalert-helper.js') }}"></script>

        <!-- Form Debug Helper (solo en desarrollo) -->
        <script src="{{ asset('js/form-debug.js') }}"></script>
    {% endblock %}
</head>
<body>
{% include 'navbar_admin.html.twig' %}

<!-- Flash messages will be handled by SweetAlert2 -->
{% if app.flashes|length > 0 %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    {% set alertType = label == 'error' ? 'error' : (label == 'success' ? 'success' : (label == 'warning' ? 'warning' : 'info')) %}
                    Swal.fire({
                        icon: '{{ alertType }}',
                        title: '{{ alertType|title }}',
                        text: '{{ message|e('js') }}',
                        timer: {{ alertType == 'success' ? 3000 : 5000 }},
                        timerProgressBar: true,
                        showConfirmButton: {{ alertType == 'error' ? 'true' : 'false' }},
                        toast: true,
                        position: 'top-end',
                        showCloseButton: true
                    });
                {% endfor %}
            {% endfor %}
        });
    </script>
{% endif %}

{% block body %}{% endblock %}
</body>
</html>
