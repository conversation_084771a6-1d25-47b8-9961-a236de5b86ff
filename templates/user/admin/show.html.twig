{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}User{% endblock %}

{% block body %}
    <h1>User</h1>

    <table class="table">
        <tbody>
            <tr>
                <th>Name</th>
                <td>{{ user.name }}</td>
            </tr>

            <tr>
                <th>Last_name</th>
                <td>{{ user.lastName }}</td>
            </tr>

            <tr>
                <th>Phone_number</th>
                <td>{{ user.phoneNumber }}</td>
            </tr>

            <tr>
                <th>Email</th>
                <td>{{ user.email }}</td>
            </tr>
            <tr>
                <th>Regiones</th>
                <td>
                    {% if user.regions|length > 0 %}
                        {% for region in user.regions %}
                            {{ region.name }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    {% else %}
                        N/A
                    {% endif %}
                </td>
            </tr>
        </tbody>
    </table>

    <a href="{{ path('app_user_admin_index', {'dominio': dominio}) }}">back to list</a>

    <a href="{{ path('app_user_admin_edit', {'id': user.id, 'dominio': dominio}) }}">edit</a>

{% endblock %}
