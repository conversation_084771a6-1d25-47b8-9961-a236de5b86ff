{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Usuarios del Sistema{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">USUARIOS DEL SISTEMA</h1>
        </div>
    </section>

    <div class="container text-center">
        {% for label, messages in app.flashes %}
            {% for message in messages %}
                <div class="alert alert-{{ label }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endfor %}

        <div class="row">
            <div class="col-12 btn-filter d-flex justify-content-between my-3">
                <div class="col-3 new-user-admin">
                    <a href="{{ path('app_user_admin_new', {'dominio': dominio}) }}" class="btn-g fw-bold">DAR DE ALTA</a>
                </div>
                <div class="col-3 filter-user d-flex align-items-center justify-content-center">
                    <img src="{{ asset('images/filter.svg') }}" alt="Filtro" class="icon-sntiasg me-2">
                    <input type="text" id="filterTitle" class="form-control w-auto" placeholder="Buscar usuario por nombre...">
                </div>
            </div>
        </div>

        <div class="table-container table-responsive px-4">
            <table class="styled-table table-striped table-bordered text-center">
                <thead class="table-primary text-dark">
                    <tr>
                        <th>USUARIOS</th>
                        <th>TELÉFONO</th>
                        <th>CORREO ELECTRÓNICO</th>
                        <th>ROL</th>
                        <th>ACCIÓN</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                        <tr data-title="{{ user.name|lower }}">
                            <td>

                                {{ user.name ~ ' ' ~ user.lastName }}
                            </td>
                            <td>{{ user.phoneNumber }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.role.name }}</td>
                            <td>
                                <a href="{{ path('app_user_admin_edit', {'id': user.id, 'dominio': dominio}) }}" class="btn-e fw-bold me-2">EDITAR</a>
                                <!--
                                <a href="{{ path('app_user_admin_edit_complete', {'id': user.id, 'dominio': dominio}) }}" class="btn btn-info btn-sm fw-bold me-2" title="Vista completa con formularios">
                                    <i class="fas fa-expand-alt"></i> COMPLETA
                                </a>
                                -->
                                {{ include('user/admin/_delete_form_admin.html.twig', {'button_label': 'ELIMINAR', 'dominio': dominio, 'user': user}) }}
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="5">SIN USUARIOS DISPONIBLES</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <p id="results-count" class="mt-3 text-white text-end fw-light">
                MOSTRANDO {{ users|length }} USUARIOS.
            </p>
            <p id="no-results" class="mt-2 text-white fw-bold" style="display: none;">
                NO SE ENCONTRARON USUARIOS.
            </p>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
   <script>
        document.addEventListener('DOMContentLoaded', function () {
            const input = document.getElementById('filterTitle');
            const rows = document.querySelectorAll('tbody tr[data-title]');
            const resultText = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            input.addEventListener('input', function () {
                const searchTerm = input.value.toLowerCase().trim();
                let count = 0;

                rows.forEach(row => {
                    const title = row.getAttribute('data-title');
                    const visible = title.includes(searchTerm);
                    row.style.display = visible ? '' : 'none';
                    if (visible) count++;
                });

                resultText.textContent = `Mostrando ${count} usuario${count === 1 ? '' : 's'}.`;
                noResults.style.display = count === 0 ? 'block' : 'none';
            });
        });
    </script>
{% endblock %}
