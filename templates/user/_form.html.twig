{{ form_start(form, {
    'action':
        user is defined and user.id is defined and user.id
            ? path(app.request.get('_route'), {'dominio': dominio, 'id': user.id})
            : path(app.request.get('_route'), {'dominio': dominio})
}) }}
  <div class="row">
    {% if form.photo is defined %}
      <div class="col-md-12 mb-3">
        {{ form_row(form.photo) }}
      </div>
    {% endif %}
  </div>

  <div class="row">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.name) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.last_name) }}
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.phone_number) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_label(form.email) }}
      {{ form_widget(form.email) }}
      {% for error in form.email.vars.errors %}
        <small class="text-danger-email mt-1">
          {{ error.message }}
        </small>
      {% endfor %}
    </div>
  </div>


  <div class="row">
    {% if form.role is defined %}
      <div class="col-md-6 col-movil margin-form-sntiasg">
        {{ form_row(form.role) }}
      </div>
    {% endif %}
    {% if form.password is defined %}
      <div class="col-md-6 col-movil margin-form-sntiasg">
        {{ form_row(form.password) }}
      </div>
    {% endif %}
  </div>

  {% if form.regions is defined %}
  <div class="row">
    <div class="col-md-12 margin-form-sntiasg">
      {{ form_row(form.regions) }}
    </div>
  </div>
  {% endif %}

  <div class="row">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.curp) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.company) }}
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.employee_number) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.birthday) }}
    </div>
  </div>

  <div class="row">
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.gender) }}
    </div>
    <div class="col-md-6 col-movil margin-form-sntiasg">
      {{ form_row(form.education) }}
    </div>
  </div>

  <div class="d-flex justify-content-center col-12 text-center mt-4">
    <button class="btn-y px-5">{{ button_label|default('CREAR') }}</button>
  </div>
{{ form_end(form) }}

{# Replace all path/url calls using dominio with app.request.attributes.get('dominio') #}
