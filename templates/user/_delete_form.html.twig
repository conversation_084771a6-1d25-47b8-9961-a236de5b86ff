{% set dominio = app.request.attributes.get('dominio') %}

<button type="button" class="btn-r" data-bs-toggle="modal" data-bs-target="#modalDeleteUser" data-form-id="delete-form-{{ user.id }}">ELIMINAR</button>

<form id="delete-form-{{ user.id }}" method="post" action="{{ path('app_user_delete', {'id': user.id, 'dominio': dominio}) }}" onsubmit="return confirm('¿Estás seguro que deseas eliminar este usuario?');">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ user.id) }}">
</form>

<div class="modal fade" id="modalDeleteUser" tabindex="-1" aria-labelledby="modalDeleteUserLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content text-center p-3">
            <div class="modal-body modal-delete">
                <h5 class="modal-title-sntiasg">¿QUIERES ELIMINAR ESTE AGREMIADO?</h5>
                <div class="d-flex justify-content-center gap-2 mt-3">
                    <button id="btnConfirmDelete" type="button" class="btn-w">Eliminar</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let formToSubmit = null;

        document.querySelectorAll('.btn-r').forEach(button => {
            button.addEventListener('click', function () {
                const formId = button.getAttribute('data-form-id');
                formToSubmit = document.getElementById(formId);
            });
        });

        const confirmBtn = document.getElementById('btnConfirmDelete');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function () {
                if (formToSubmit) {
                    formToSubmit.submit();
                    formToSubmit = null;
                }
            });
        }
    });
</script>
