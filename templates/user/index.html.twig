{% extends 'base.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Agremiados{% endblock %}

{% block body %}
    <section class="header-sntiasg-b">
        <div class="container-fluid container-header">
            <h1 class="title-sntiasg">AGREMIADOS</h1>
        </div>
    </section>

    <div class="container text-center">
        <div class="row">
            <div class="col-12 d-flex justify-content-end my-3">
                <div class="d-flex align-items-center">
                    <img src="{{ asset('images/filter.svg') }}" alt="Filtro" class="icon-sntiasg me-2">
                    <input type="text" id="filterTitle" class="form-control w-auto" placeholder="Buscar agremiado por nombre...">
                </div>
            </div>
            <div class="col-12 d-flex justify-content-between my-3">
                <div class="col-3 new-user">
                    <a href="{{ path('app_user_new', {'dominio': dominio}) }}" class="btn-g fw-bold ">DAR DE ALTA</a>
                </div>
                <div class="col-3 massive-load">
                    <button class="btn-o text-white fw-bold mb-2" data-bs-toggle="modal" data-bs-target="#modalCargaMasiva">
                        CARGA DE AGREMIADOS
                    </button>
                </div>

                {# Modal para carga masiva #}
                <div class="modal fade" id="modalCargaMasiva" tabindex="-1" aria-labelledby="modalCargaMasivaLabel" aria-hidden="true">
                    <div class="modal-dialog modal-a">
                        <a href="{{ path('app_user_index', {'dominio': dominio}) }}" class="btn-regresar-icon position-absolute top-0 end-0 m-3">
                            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path id="Vector" d="M17.9999 17.9999L10.5 10.5M10.5 10.5L3 3M10.5 10.5L18 3M10.5 10.5L3 18" stroke="white" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <div class="modal-content">
                            <div class="modal-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="modal-title-sntiasg" id="modalCargaMasivaLabel">Carga masiva de usuarios</h5>
                                </div>
                                <form action="{{ path('app_user_bulk_upload', {'dominio': dominio}) }}" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <input type="file" name="excel_file" accept=".xlsx,.xls" required class="form-control">
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" class="btn btn-success fw-bold">Subir usuarios</button>
                                        <a href="{{ path('app_user_download_template', {'dominio': dominio}) }}" class="btn btn-warning fw-bold">
                                            Descargar plantilla
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-container table-responsive px-4">
            <table class="styled-table table-striped table-bordered text-center">
                <thead class="table-primary text-dark">
                    <tr>
                        <th>NOMBRE</th>
                        <th>APELLIDOS</th>
                        <th>EMPRESA</th>
                        <th>FECHA DE NACIMIENTO</th>
                        <th>TELÉFONO</th>
                        <th>CORREO ELECTRÓNICO</th>
                        <th>N° DE EMPLEADO</th>
                        <th>CURP</th>
                        <th>GENERO</th>
                        <th>EDUCACIÓN</th>
                        <th>ACCIONES</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                        <tr data-title="{{ (user.name ~ ' ' ~ user.lastName)|lower }}">
                            <td>{{ user.name }}</td>
                            <td>{{ user.lastName }}</td>
                            <td>{{ user.company ? user.company.name : '' }}</td>
                            <td>{{ user.birthday ? user.birthday|date('d/m/Y') : 'N/A' }}</td>
                            <td>{{ user.phoneNumber }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.employeeNumber }}</td>
                            <td>{{ user.curp }}</td>
                            <td>{{ user.gender }}</td>
                            <td>{{ user.education }}</td>
                            <td>
                                <a href="{{ path('app_user_show', {'id': user.id, 'dominio': dominio}) }}" class="btn-v fw-bold">VER</a> 

                                <a href="{{ path('app_user_edit', {'id': user.id, 'dominio': dominio}) }}" class="btn-e fw-bold">EDITAR</a>

                                {{ include('user/_delete_form.html.twig', {'button_label': 'ELIMINAR'}) }}
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="8">SIN AGREMIADOS DISPONIBLES</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <p id="results-count" class="mt-3 text-white text-end fw-light">
                MOSTRANDO {{ users|length }} AGREMIADOS
            </p>
            <p id="no-results" class="mt-2 text-white fw-bold" style="display: none;">
                NO SE ENCONTRARON AGREMIADOS.
            </p>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const input = document.getElementById('filterTitle');
            const rows = document.querySelectorAll('tbody tr[data-title]');
            const resultText = document.getElementById('results-count');
            const noResults = document.getElementById('no-results');

            input.addEventListener('input', function () {
                const searchTerm = input.value.toLowerCase().trim();
                let count = 0;

                rows.forEach(row => {
                    const title = row.getAttribute('data-title');
                    const visible = title.includes(searchTerm);
                    row.style.display = visible ? '' : 'none';
                    if (visible) count++;
                });

                resultText.textContent = `Mostrando ${count} agremiad${count === 1 ? 'o' : 'os'}.`;
                noResults.style.display = count === 0 ? 'block' : 'none';
            });
        });
    </script>
{% endblock %}
