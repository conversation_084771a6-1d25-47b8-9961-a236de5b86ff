{% extends 'base-login.html.twig' %}

{% set dominio = app.request.attributes.get('dominio') %}

{% block title %}Inicio de sesión{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .logo-login {
            animation: pulse 2s infinite;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4))
                    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))
                    drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
{% endblock %}

{% block body %}
<body class="bg-dark text-white d-flex align-items-center justify-content-center vh-100">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-7 col-lg-6">
        <div class="login-wrapper p-4 text-white">
          <div class="text-center mb-4 d-flex flex-column align-items-center">
            <img src="/images/logos/logoTS.svg" alt="Logo" class="logo-login mb-2" />
            <h2 class="login-title">ACCESO ADMINISTRADOR</h2>
          </div>

          {% if error %}
              <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
          {% endif %}

          <form method="post">
            <div class="form-group mb-3 d-flex flex-column align-items-center">
              <label for="inputEmail" class="form-label fw-light text-login">CORREO ELECTRÓNICO</label>
              <input type="email" value="{{ last_username }}" name="email" id="inputEmail" class="form-control rounded-pill input-field" autocomplete="email" required autofocus>
            </div>

            <div class="form-group mb-3 d-flex flex-column align-items-center">
              <label for="inputPassword" class="form-label fw-light text-login">CONTRASEÑA</label>
              <input type="password" name="password" id="inputPassword" class="form-control rounded-pill input-field" autocomplete="current-password" required>
            </div>

            <div class="form-group mb-3 d-flex flex-column align-items-center">
              <div class="checkbox mb-3">
                <label>
                  <input type="checkbox" name="_remember_me" class="form-check-input"> Recordarme
                </label>
              </div>
            </div>

            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">

            <div class="d-flex flex-column align-items-center">
              <button class="btn btn-lg btn-primary rounded-pill btn-login" type="submit">
                INICIAR SESIÓN
              </button>
              <a href="{{ path('forget_password', {'dominio': dominio}) }}" class="btn btn-link text-white mt-2">¿Olvidaste tu contraseña?</a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</body>
{% endblock %}
