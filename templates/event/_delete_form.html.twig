{% set dominio = app.request.attributes.get('dominio') %}

<form id="delete-form-{{ event.id }}" method="post" action="{{ path('app_event_delete', {'id': event.id, 'dominio': dominio}) }}" onsubmit="return confirm('Are you sure you want to delete this item?');">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ event.id) }}">
    <button type="button" class="btn-delete-event" data-bs-toggle="modal" data-bs-target="#modalDeleteEvent" data-form-id="delete-form-{{ event.id }}">ELIMINAR</button>
</form>

<div class="modal fade" id="modalDeleteEvent" tabindex="-1" aria-labelledby="modalDeleteEventLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content text-center p-3">
            <div class="modal-body modal-delete">
                <h5 class="modal-title-sntiasg" id="modalDeleteEventLabel">¿Eliminar evento?</h5>
                <div class="d-flex justify-content-center gap-2 mt-3">
                    <button id="btnConfirmDelete" type="button" class="btn-w">ELIMINAR</button>
                </div>
            </div>
        </div>
    </div>
</div>
  
<script>
    document.addEventListener('DOMContentLoaded', function () {
        let formToSubmit = null;
    
        document.querySelectorAll('.btn-delete-event').forEach(button => {
        button.addEventListener('click', function () {
            formToSubmit = button.closest('form');
        });
        });
    
        const confirmBtn = document.getElementById('btnConfirmDelete');
        if (confirmBtn) {
        confirmBtn.addEventListener('click', function () {
            if (formToSubmit) {
            formToSubmit.submit();
            formToSubmit = null;
            }
        });
        }
    });
</script>
