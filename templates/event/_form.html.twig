{% if event.id is defined and event.id %}
    {{ form_start(form, { 'action': path('app_event_edit', {'id': event.id, 'dominio': dominio}), 'method': 'POST' }) }}
{% else %}
    {{ form_start(form, { 'action': path('app_event_new', {'dominio': dominio}), 'method': 'POST' }) }}
{% endif %}

    <div class="row form-movil">
        {{ form_row(form.title) }}
    </div>

    <div class="row">
        <div class="col-md-12 margin-form-sntiasg">
            <label class="modal-text-sntiasg">Regiones</label>
            <div class="region-selector" id="region-selector">
                <!-- Regions will be loaded here -->
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row form-movil">
        {{ form_row(form.start_date) }}
        {{ form_row(form.end_date) }}
    </div>

    <div class="row form-movil">
        {{ form_row(form.image) }}
    </div>

    <div class="row form-movil">
        {{ form_row(form.description) }}
    </div>

    <!-- Hidden input to store selected companies -->
    <input type="hidden" name="selected_companies" id="selected-companies" value="{% if event.companies is defined and event.companies|length > 0 %}{{ event.companies|map(c => c.id)|join(',') }}{% endif %}">

    <div class="row d-flex justify-content-center">
        <button class="btn-y col-md-4">{{ button_label|default('CREAR EVENTO') }}</button>
    </div>

    <!-- Modal for company selection -->
    <div class="modal fade" id="companiesModal" tabindex="-1" aria-labelledby="companiesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-a">
            <a href="{{ path('app_event_index', {'dominio': dominio}) }}" class="btn-regresar-icon position-absolute top-0 end-0 m-3">
                <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path id="Vector" d="M17.9999 17.9999L10.5 10.5M10.5 10.5L3 3M10.5 10.5L18 3M10.5 10.5L3 18" stroke="white" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            <div class="modal-content">
                <div class="modal-body">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <h5 class="modal-title-sntiasg m-0" id="companiesModalLabel">Seleccionar Empresas</h5>
                    </div>

                    <div id="companies-container"></div>

                    <div class="d-flex justify-content-center align-items-center mt-5">
                        <button type="button" class="btn-y" id="save-companies">Guardar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
{{ form_end(form) }}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Store selected companies for each region
        const selectedCompaniesByRegion = {};
        let currentRegionId = null;
        let dominio = '{{ dominio }}';
        // Get initially selected companies
        const initialSelectedCompanies = document.getElementById('selected-companies').value;
        const initialSelectedCompanyIds = initialSelectedCompanies ? initialSelectedCompanies.split(',') : [];

        // Load regions
        fetch('{{ path('app_region_list', {'dominio': dominio}) }}')
            .then(response => response.json())
            .then(regions => {
                const regionSelector = document.getElementById('region-selector');
                regionSelector.innerHTML = '';

                regions.forEach(region => {
                    const regionDiv = document.createElement('div');
                    regionDiv.className = 'form-check mb-2';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'form-check-input region-checkbox';
                    checkbox.id = `region-${region.id}`;
                    checkbox.dataset.regionId = region.id;

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = `region-${region.id}`;
                    label.textContent = region.name;

                    regionDiv.appendChild(checkbox);
                    regionDiv.appendChild(label);
                    regionSelector.appendChild(regionDiv);

                    // Add event listener to checkbox
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            currentRegionId = this.dataset.regionId;
                            loadCompaniesForRegion(currentRegionId);
                        }
                    });

                    // Load companies for this region to check if any are selected
                    if (initialSelectedCompanyIds.length > 0) {
                        fetch(`/${dominio}/region/${region.id}/companies`)
                            .then(response => response.json())
                            .then(companies => {
                                // Check if any companies in this region are selected
                                const regionCompanyIds = companies.map(c => c.id.toString());
                                const hasSelectedCompanies = initialSelectedCompanyIds.some(id => regionCompanyIds.includes(id));

                                if (hasSelectedCompanies) {
                                    // Mark this region as checked
                                    checkbox.checked = true;

                                    // Store selected companies for this region
                                    selectedCompaniesByRegion[region.id] = initialSelectedCompanyIds.filter(id =>
                                        regionCompanyIds.includes(id)
                                    );
                                }
                            })
                            .catch(error => {
                                console.error('Error loading companies for region:', error);
                            });
                    }
                });
            })
            .catch(error => {
                console.error('Error loading regions:', error);
                document.getElementById('region-selector').innerHTML = '<div class="alert alert-danger">Error al cargar las regiones</div>';
            });

        // Load companies for a region
        function loadCompaniesForRegion(regionId) {
            fetch(`/${dominio}/region/${regionId}/companies`)
                .then(response => response.json())
                .then(companies => {
                    const companiesContainer = document.getElementById('companies-container');
                    companiesContainer.innerHTML = '';

                    if (companies.length === 0) {
                        companiesContainer.innerHTML = '<div class="alert alert-info">No hay empresas en esta región</div>';
                        return;
                    }

                    // If we don't have selected companies for this region yet, select all by default
                    if (!selectedCompaniesByRegion[regionId]) {
                        selectedCompaniesByRegion[regionId] = companies.map(company => company.id.toString());
                    }

                    companies.forEach(company => {
                        const companyDiv = document.createElement('div');
                        companyDiv.className = 'form-check mb-2';

                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.className = 'form-check-input company-checkbox';
                        checkbox.id = `company-${company.id}`;
                        checkbox.value = company.id;
                        checkbox.checked = selectedCompaniesByRegion[regionId].includes(company.id.toString());

                        const label = document.createElement('label');
                        label.className = 'form-check-label';
                        label.htmlFor = `company-${company.id}`;
                        label.textContent = company.name;

                        companyDiv.appendChild(checkbox);
                        companyDiv.appendChild(label);
                        companiesContainer.appendChild(companyDiv);
                    });

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('companiesModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading companies:', error);
                    alert('Error al cargar las empresas');
                });
        }

        // Save selected companies
        document.getElementById('save-companies').addEventListener('click', function() {
            const selectedCompanies = [];
            document.querySelectorAll('.company-checkbox:checked').forEach(checkbox => {
                selectedCompanies.push(checkbox.value);
            });

            selectedCompaniesByRegion[currentRegionId] = selectedCompanies;

            // Update hidden input with all selected companies
            const allSelectedCompanies = [];
            for (const regionId in selectedCompaniesByRegion) {
                allSelectedCompanies.push(...selectedCompaniesByRegion[regionId]);
            }

            // Remove duplicates
            const uniqueCompanies = [...new Set(allSelectedCompanies)];
            document.getElementById('selected-companies').value = uniqueCompanies.join(',');

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('companiesModal'));
            modal.hide();
        });

        // Handle form submission
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // Make sure we have at least one company selected
            const selectedCompanies = document.getElementById('selected-companies').value;
            if (!selectedCompanies && document.querySelectorAll('.region-checkbox:checked').length > 0) {
                event.preventDefault();
                alert('Por favor seleccione al menos una empresa');
            }
        });
    });
</script>
