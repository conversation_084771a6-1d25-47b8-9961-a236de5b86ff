{% extends 'base.html.twig' %}

{% block title %}Inbox{% endblock %}

{% block body %}
    <section class="container">
        <header style="
            margin-top: 15vh;
            display: flex;
            flex-direction: row;
            border-radius: 10px;
            overflow: hidden;
            border-color: rgba(198,118,24);
            border-width: 1px;
            border-style: solid;
        ">
            <div style="
                width: 10%;
                background-color: #C67618;
                justify-content: center;
                align-content: center;
                display: flex;
                flex-direction: column;
            ">
                <a  style="
                    font-size: 25px;
                    text-align: center;
                ">
                        Regresar
                </a>
            </div>
            <div style="
                width: 90%;
                display: flex;
                flex-direction: row;
                padding-left: 3vh ;
                align-content: center;
            ">
                <h1 style="
                    color: #fff;
                ">{{ sender }}</h1>
            </div>
        </header>

        <body >
            <div style="
                height: 65vh;
            ">
                {% for message in messages %}
                    <div class="message">
                        <strong>{{ message.sender }}:</strong>
                        <span>{{ message.message }}</span>
                        <span class="time">{{ message.time|date('H:i:s') }}</span>
                    </div>
                {% else %}
                    <p>No hay mensajes.</p>
                {% endfor %}
            </div>
            <div style="border-radius: 10px; overflow: hidden; height: 6vh; display: flex; flex-direction: row">
                <input style="
                    width: 90%;
                    height: 100%;
                    ">
                <div style="
                    width: 10%;
                    background-color: #FFFFFf;
                    justify-content: center;
                    align-content: center;
                    display: flex;
                    flex-direction: column;
                ">
                    <a>
                        <p style="
                        font-size: 25px;
                        text-align: center;
                        color: #0a0a0a;
                    ">enviar</p>
                    </a>
                </div>
            </div>
        </body>
    </section>
{% endblock %}