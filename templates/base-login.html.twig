<!DOCTYPE html>
<html>
    {% set dominio = app.request.attributes.get('dominio') %}


    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{% block title %}CTM{% endblock %} | ctm.mx</title>
        
        <!-- Fuentes Spartan (¡Elegantes como Stewie!) -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=League+Spartan:wght@100..900&display=swap" rel="stylesheet">
        
        {% block stylesheets %}
            <!-- Bootstrap + SweetAlert2 CSS -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
            
            {{ encore_entry_link_tags('app') }}
            <link rel="logo" type="image/png" href="{{ asset('images/logo.png') }}">
            
            <!-- FullCalendar -->
            <link href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@6.1.8/main.min.css" rel="stylesheet" />
            <link href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@6.1.8/main.min.css" rel="stylesheet" />
        {% endblock %}

        <!-- JS Routing -->
        <script src="{{ asset('bundles/fosjsrouting/js/router.min.js') }}"></script>
        <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}"></script>
    </head>
    <body>
        {% block body %}{% endblock %}

        {% block javascripts %}
            <!-- SweetAlert2 JS (¡Más poderoso que mi rayo desintegrador!) -->
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            
            <!-- Bootstrap JS -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
            
            {{ encore_entry_script_tags('app') }}
            
            <!-- FullCalendar JS -->
            <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/core@6.1.8/main.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@6.1.8/main.min.js"></script>

            <script>
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.addEventListener('mouseenter', Swal.stopTimer)
                        toast.addEventListener('mouseleave', Swal.resumeTimer)
                    }
                });
                window.Swal = Swal;
                window.Toast = Toast;
            </script>
        {% endblock %}
    </body>
</html>