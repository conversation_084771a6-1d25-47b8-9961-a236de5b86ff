<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="Somos un equipo comprometido con transformar vidas, promoviendo el bienestar y el crecimiento de los trabajadores y sus familias. Fomentamos la armonía entre empresas y empleados para un futuro laboral más justo y equitativo.">
    <meta name="keywords" content="sindicato, transformación sindical, bienestar laboral, crecimiento de trabajadores, armonía laboral, derechos laborales, modernización sindical, conciliación empresa-trabajador, equidad laboral, formación sindical, trabajadores y familias, fortalecimiento sindical, empleo digno, relaciones laborales">
    <meta name="author" content="Masoftcode">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Credencial | transformacionsindical.mx</title>
    <link rel="icon" type="image/png" href="{{ asset('images/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/keen-slider@6.8.5/keen-slider.min.css"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=PT+Sans+Caption:wght@400;700&display=swap" rel="stylesheet">

    <style>
        .body {
            background: linear-gradient(to bottom,
            #1C4488 0%,
            #08B5B1 45%,
            #4BBE7E 100%
            );
            box-sizing: border-box;
            min-height: 100%;
            overflow-x: hidden;
            max-width: 100%;
            width: 100%;
            height: 100vh;
        }
        .firma-digital{
            width: 20vh;
            height: 20vh;
        }
        .fondo-card {
            background-image: url('{{ asset("images/users/credencial_fondo.jpg") }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 35px;
            box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.3);
            padding: 40px;
        }
        .subtitle-ts {
            font-family: "PT Sans Caption", serif;
            font-weight: 700;
            font-style:normal;
            font-size: 75px;
            color: #FFFFFF !important;
            margin: 20px 0;
            text-align: center;
        }
        video, canvas, img {
            max-width: 100%;
            border-radius: 10px;
            margin: 10px 0;
        }
        #loa
        label {
            font-weight: 500;
            margin-bottom: 3px;
        }
        input[type="file"] {
            background-color: #ffffff;
            padding: 10px;
            border-radius: 10px;
            width: 100%;
            color: #333;
            margin-top: 5px;
        }
        .are-text p {
            text-align: end;
        }
        .carousel-logo img {
            width: 150px;
            height: auto;
        }
        input[type="file"]{

            width: 100%;
            padding: 10px;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            text-transform: uppercase;

        }
        input[type="file"]
        {
            background-color: #7CC13E;
            color: #000000;
            border-radius: 30px;
            padding: 5px;
            border: none;
        }
        .row.are {
            margin-top: 60px;
        }
        .row {
            width: 100%;
        }

        @media (max-width: 575px) {
            .subtitle-ts {
                font-size: 30px;
            }
            .fondo-card {
                background-image: url('{{ asset("images/users/credencial_fondo_mobile.png") }}');
            }
            .info-span {
                font-size: 12px;
                padding: 6px;
            }

            .info-text {
                font-size: 12px;
                margin-bottom: 4px;
                font-weight: bold;
                color: white;
            }

            .profile-photo {
                width: 90px;
                height: 90px;
                object-fit: cover;
                border-radius: 10px;
            }

            .firma-digital {
                width: 80px;
                height: auto;
                margin-top: 10px;
            }

            .container.cont-credential {
                padding: 10px;
            }

            .mb-2 {
                margin-bottom: 0.5rem !important;
            }

            .mt-3 {
                margin-top: 1rem !important;
            }

            .row.w-100 {
                gap: 10px;
            }
        }

        span.navbar-toggler-icon {
            color: #FFFFFF !important;
        }
        button.navbar-toggler {
            color: transparent !important;
            border: 0 solid transparent !important;
        }
        /* Beneficios */
        .row-cols-3>* {
            width: 50% !important;
        }
        @media (min-width: 576px) and (max-width: 768px) {
            .subtitle-ts {
                font-size: 30px;
            }

            span.navbar-toggler-icon {
                color: #FFFFFF !important;
            }
            button.navbar-toggler {
                color: transparent !important;
            }
            .carousel-logo img {
                width: 70px;
                height: auto;
            }
            .are-text p {
                text-align: center;
            }
            /* Beneficios */
            .row-cols-3>* {
                width: 50% !important;
            }
            .row {
                width: 100% !important;
            }
        }
        @media (min-width: 768px) and (max-width: 992px) {
            .subtitle-ts {
                font-size: 35px;
            }
            span.navbar-toggler-icon {
                color: #FFFFFF !important;
            }
            button.navbar-toggler {
                color: transparent !important;
            }
            .carousel-logo img {
                width: 100px;
                height: auto;
            }
            /* Beneficios */
            .row-cols-3>* {
                width: 50% !important;
            }
            .row {
                width: 100% !important;
            }
        }
        @media (min-width: 992px) and (max-width: 1200px) {
            .subtitle-ts {
                font-size: 40px;
            }
            .row {
                width: 100% !important;
            }
        }
        @media (min-width: 1200px) and (max-width: 1400px) {
            .subtitle-ts {
                font-size: 45px;
            }
            .row {
                width: 100% !important;
            }
        }
        @media (min-width: 1400px) and (max-width: 1900px) {
            .subtitle-ts {
                font-size: 50px;
            }
        }
        @media (min-width: 1900px) {
            .subtitle-ts {
                font-size: 75px;
            }
        }

    </style>
</head>

<body class="body">

<section>
    <h2 class="subtitle-ts text-center mb-2">MI CREDENCIAL</h2>

    <div class="container cont-credential rounded-5 p-1 my-5 d-flex justify-content-center fondo-card">

    <div class="row w-100 align-items-center flex-column flex-md-row">
            <div class="info-movil d-flex flex-column align-items-center justify-content-center">
                <img src="{{ asset('images/isotipo.svg') }}" alt="Isotipo" class="iso-credential mb-3">
            </div>

        <div class="col-md-4 photo-movil d-flex flex-column align-items-center justify-content-center">
            {% if user.photo is not empty %}
                <img src="{{ asset('uploads/' ~ user.photo) }}" alt="Foto de perfil" class="profile-photo mb-3">
            {% endif %}
        </div>


            <div class="col-md-4 info-movil d-flex flex-column align-items-center justify-content-center">

                <p class="info-text">NOMBRE</p>
                <div class="info-span bg-white p-2 rounded shadow-sm text-center w-100 mb-2">
                    <p class="mb-0 text-dark">{{ user.name ~ " " ~ user.lastname }}</p>
                </div>

                <p class="info-text">NÚMERO DE TELÉFONO</p>
                <div class="info-span bg-white p-2 rounded shadow-sm text-center w-100 mb-2">
                    <p class="mb-0 text-dark">{{ user.phonenumber }}</p>
                </div>

                <p class="info-text">CORREO ELECTRÓNICO</p>
                <div class="info-span bg-white p-2 rounded shadow-sm text-center w-100 mb-2">
                    <p class="mb-0 text-dark">{{ user.email }}</p>
                </div>

                <p class="info-text">EMPRESA</p>
                <div class="info-span bg-white p-2 rounded shadow-sm text-center w-100 mb-2">
                    <p class="mb-0 text-dark">{{ user.company.name }}</p>
                </div>

                <p class="info-text">NÚMERO DE EMPLEADO</p>
                <div class="info-span bg-white p-2 rounded shadow-sm text-center w-100 mb-2">
                    <p class="mb-0 text-dark">N°{{ user.employeenumber }}</p>
                </div>


            </div>

            <div class="col-md-4 photo-movil d-flex flex-column align-items-center justify-content-center">
                <img src="{{ asset('images/firma.png') }}" alt="Firma" class="firma-digital mt-3">
            </div>

        </div>
    </div>
</section>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://kit.fontawesome.com/9278dceefd.js" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/keen-slider@6.8.5/keen-slider.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.15/index.global.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
