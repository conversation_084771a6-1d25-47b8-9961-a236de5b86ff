{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "api-platform/doctrine-orm": ">=4.1.18", "api-platform/symfony": ">=4.1.18", "ctwillie/expo-server-sdk-php": "^2.1", "doctrine/dbal": "^3.9.5", "doctrine/doctrine-bundle": "^2.15", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.5.0", "friendsofsymfony/jsrouting-bundle": "^3.5.2", "kreait/firebase-bundle": ">=5.6", "lexik/jwt-authentication-bundle": "^3.1.1", "nelmio/api-doc-bundle": "^5.4.0", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.6.2", "phpoffice/phpspreadsheet": "^4.4", "phpstan/phpdoc-parser": "^2.1", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2.8.0", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/google-mailer": "7.2.*", "symfony/http-client": "7.2.*", "symfony/http-client-contracts": ">=3.6.0", "symfony/lock": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mercure-bundle": ">=0.3.9", "symfony/mime": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/rate-limiter": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/security-csrf": "7.2.*", "symfony/serializer": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/webpack-encore-bundle": "^2.2", "symfony/yaml": "7.2.*", "twilio/sdk": "^8.6.4"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"phpunit/phpunit": "^10.5.47", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/maker-bundle": "^1.64.0", "symfony/phpunit-bridge": "^7.3.1", "symfony/debug-bundle": "7.2.*", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}